#!/usr/bin/env python3
"""
立即执行绩效数据同步任务
连接数据库并执行 performance_sync_scheduler.py 中的方法
"""
import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db.session import SessionLocal
from app.schedule.performance_sync_scheduler import PerformanceSyncTaskWrapper
from app.logging_config import setup_logging, get_task_logger

# 设置日志
setup_logging()
logger = get_task_logger("execute_performance_sync")


def test_database_connection():
    """测试数据库连接"""
    try:
        db = SessionLocal()
        # 执行一个简单的查询来测试连接
        result = db.execute(text("SELECT 1"))
        db.close()
        logger.info("数据库连接测试成功")
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False


def execute_batch_sync():
    """执行批量绩效数据同步"""
    logger.info("开始执行批量绩效数据同步任务")
    
    try:
        # 创建批量同步任务
        sync_task = PerformanceSyncTaskWrapper(
            sync_type="batch",
            batch_size=50
        )
        
        # 执行任务
        result = sync_task.run()
        logger.info(f"批量同步任务执行完成，结果: {result}")
        return result
        
    except Exception as e:
        logger.error(f"批量同步任务执行失败: {e}")
        raise


def execute_oog120_sync():
    """执行OOG120项目绩效数据同步"""
    logger.info("开始执行OOG120项目绩效数据同步任务")
    
    try:
        # 创建OOG120项目同步任务
        sync_task = PerformanceSyncTaskWrapper(
            project_code="OOG120",
            sync_type="batch",
            batch_size=30
        )
        
        # 执行任务
        result = sync_task.run()
        logger.info(f"OOG120项目同步任务执行完成，结果: {result}")
        return result
        
    except Exception as e:
        logger.error(f"OOG120项目同步任务执行失败: {e}")
        raise


def main():
    """主函数"""
    logger.info("=== 开始执行绩效数据同步任务 ===")
    logger.info(f"执行时间: {datetime.now()}")
    
    # 1. 测试数据库连接
    if not test_database_connection():
        logger.error("数据库连接失败，无法继续执行")
        return False
    
    success = True
    
    try:
        # 2. 执行批量同步任务
        logger.info("执行批量绩效数据同步...")
        batch_result = execute_batch_sync()
        
        # 3. 执行OOG120项目同步任务
        logger.info("执行OOG120项目绩效数据同步...")
        oog120_result = execute_oog120_sync()
        
        logger.info("=== 所有同步任务执行完成 ===")
        logger.info(f"批量同步结果: {batch_result}")
        logger.info(f"OOG120同步结果: {oog120_result}")
        
    except Exception as e:
        logger.error(f"同步任务执行过程中出现错误: {e}")
        success = False
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("✅ 绩效数据同步任务执行成功")
            sys.exit(0)
        else:
            print("❌ 绩效数据同步任务执行失败")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        print("⚠️ 任务被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        print(f"❌ 程序执行异常: {e}")
        sys.exit(1)
