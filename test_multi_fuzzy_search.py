#!/usr/bin/env python3
"""
测试KOL多模糊匹配搜索功能
"""
import requests
import json
from typing import List, Dict, Any

# API基础URL
BASE_URL = "http://localhost:8000/api/v1/kols"

def test_multi_fuzzy_search():
    """测试多模糊匹配搜索功能"""
    
    print("🔍 测试KOL多模糊匹配搜索功能")
    print("=" * 50)
    
    # 测试用例1: hashtags多模糊匹配
    test_cases = [
        {
            "name": "Hashtags多模糊匹配",
            "payload": {
                "hashtags_fuzzy": ["beauty", "makeup", "fashion"]
            },
            "description": "搜索包含beauty、makeup或fashion的hashtags"
        },
        {
            "name": "Topics多模糊匹配",
            "payload": {
                "topics_fuzzy": ["时尚", "美妆", "生活"]
            },
            "description": "搜索包含时尚、美妆或生活的topics"
        },
        {
            "name": "Captions多模糊匹配",
            "payload": {
                "captions_fuzzy": ["穿搭", "教程", "分享"]
            },
            "description": "搜索包含穿搭、教程或分享的captions"
        },
        {
            "name": "混合多模糊匹配",
            "payload": {
                "hashtags_fuzzy": ["beauty", "fashion"],
                "topics_fuzzy": ["时尚", "美妆"],
                "captions_fuzzy": ["穿搭", "教程"]
            },
            "description": "同时搜索多个字段的多模糊匹配"
        },
        {
            "name": "单关键词兼容性测试",
            "payload": {
                "hashtags_fuzzy": ["beauty"],
                "platform": "TIKTOK"
            },
            "description": "测试单关键词是否仍然工作"
        },
        {
            "name": "精确匹配+模糊匹配组合",
            "payload": {
                "hashtags": ["beauty"],
                "topics_fuzzy": ["时尚", "生活"],
                "min_followers": 10000
            },
            "description": "测试精确匹配和模糊匹配的组合使用"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print(f"📝 描述: {test_case['description']}")
        print(f"🔧 请求参数: {json.dumps(test_case['payload'], ensure_ascii=False, indent=2)}")
        
        try:
            # 发送POST请求到搜索端点
            response = requests.post(
                f"{BASE_URL}/search",
                json=test_case['payload'],
                params={"skip": 0, "limit": 10},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 请求成功")
                print(f"📊 结果统计: 找到 {data['total']} 个KOL，返回 {len(data['items'])} 个")
                
                # 显示前3个结果的相关字段
                if data['items']:
                    print("🎯 前3个结果示例:")
                    for j, kol in enumerate(data['items'][:3], 1):
                        print(f"  {j}. ID: {kol['id']}, 昵称: {kol['nick_name']}")
                        if kol.get('hashtags'):
                            print(f"     Hashtags: {kol['hashtags'][:3]}...")
                        if kol.get('topics'):
                            print(f"     Topics: {kol['topics'][:3]}...")
                        if kol.get('captions'):
                            print(f"     Captions: {kol['captions'][:3]}...")
                else:
                    print("📭 没有找到匹配的结果")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
        
        print("-" * 40)

def test_performance_comparison():
    """测试性能对比"""
    print("\n⚡ 性能对比测试")
    print("=" * 50)
    
    import time
    
    # 单关键词搜索
    start_time = time.time()
    response1 = requests.post(
        f"{BASE_URL}/search",
        json={"hashtags_fuzzy": ["beauty"]},
        params={"skip": 0, "limit": 100}
    )
    single_time = time.time() - start_time
    
    # 多关键词搜索
    start_time = time.time()
    response2 = requests.post(
        f"{BASE_URL}/search",
        json={"hashtags_fuzzy": ["beauty", "makeup", "fashion", "lifestyle", "skincare"]},
        params={"skip": 0, "limit": 100}
    )
    multi_time = time.time() - start_time
    
    print(f"🔍 单关键词搜索耗时: {single_time:.3f}秒")
    print(f"🔍 多关键词搜索耗时: {multi_time:.3f}秒")
    print(f"📈 性能比率: {multi_time/single_time:.2f}x")
    
    if response1.status_code == 200 and response2.status_code == 200:
        data1 = response1.json()
        data2 = response2.json()
        print(f"📊 单关键词结果数: {data1['total']}")
        print(f"📊 多关键词结果数: {data2['total']}")

if __name__ == "__main__":
    print("🚀 开始测试KOL多模糊匹配搜索功能")
    print("请确保API服务器正在运行在 http://localhost:8000")
    
    try:
        # 测试服务器连接
        response = requests.get(f"{BASE_URL}/stats")
        if response.status_code == 200:
            print("✅ API服务器连接正常")
            test_multi_fuzzy_search()
            test_performance_comparison()
        else:
            print("❌ API服务器连接失败")
    except requests.exceptions.RequestException:
        print("❌ 无法连接到API服务器，请检查服务器是否运行")
    
    print("\n🎉 测试完成!")
