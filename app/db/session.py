from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import async_sessionmaker, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.config import settings

# 数据库连接字符串
database_uri = str(settings.DATABASE_URI)

# 同步引擎和会话
engine = create_engine(
    database_uri,
    pool_pre_ping=True,
    echo=False,
    pool_size=10,           # 连接池保持的默认连接数（默认5）
    max_overflow=20,        # 允许临时创建的额外连接数（默认10）
    pool_timeout=60,        # 获取连接的超时时间（秒，默认30）
    pool_recycle=300        # 连接空闲300秒后自动回收（避免连接失效）
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 异步引擎和会话
# 如果是SQLite，使用aiosqlite作为异步引擎
if 'sqlite' in database_uri:
    async_db_uri = database_uri.replace('sqlite', 'sqlite+aiosqlite')
else:
    async_db_uri = database_uri.replace("postgresql+psycopg2", "postgresql+asyncpg")
    
async_engine = create_async_engine(
    async_db_uri,
    pool_pre_ping=True,
    echo=False,
    pool_size=10,
    max_overflow=20,
    pool_timeout=60,
    pool_recycle=300
)
AsyncSessionLocal = async_sessionmaker(
    autocommit=False, autoflush=False, bind=async_engine
)


# 同步数据库依赖项
def get_db():
    """获取同步数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# 异步数据库依赖项
async def get_async_db():
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        yield session 