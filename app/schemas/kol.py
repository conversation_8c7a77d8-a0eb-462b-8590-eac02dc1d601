"""
KOL相关的Pydantic schemas
"""
from datetime import datetime
from typing import Optional, List, Union
from decimal import Decimal
from pydantic import BaseModel, Field, EmailStr, field_validator

from app.models.enums import PlatformEnum, KOLTierEnum


class KOLBase(BaseModel):
    """KOL基础schema"""
    platform: PlatformEnum = Field(..., description="KOL所在平台")
    nick_name: str = Field(..., description="KOL昵称", max_length=255)
    project_code: str = Field(..., description="项目编码", max_length=50)
    email: Optional[str] = Field(None, description="KOL邮箱")
    bio: Optional[str] = Field(None, description="KOL简介")
    followers_count: Optional[int] = Field(None, description="粉丝数", ge=0)
    likes_count: Optional[int] = Field(None, description="点赞数", ge=0)
    source: Optional[str] = Field(None, description="KOL来源", max_length=50)
    engagement_rate: Optional[Decimal] = Field(None, description="互动率", ge=0, le=1)
    mean_views_k: Optional[Decimal] = Field(None, description="平均观看数（千次）", ge=0)
    median_views_k: Optional[Decimal] = Field(None, description="中位观看数（千次）", ge=0)
    tier: Optional[KOLTierEnum] = Field(None, description="KOL分级")
    hashtags: Optional[List[str]] = Field(default_factory=list, description="hashtag数组")
    captions: Optional[List[str]] = Field(default_factory=list, description="caption数组")
    topics: Optional[List[str]] = Field(default_factory=list, description="topic数组")
    crawler_task_id: Optional[int] = Field(None, description="爬虫任务ID")
    note: Optional[str] = Field(None, description="备注")

    # Email获取状态相关字段
    bio_parsed_at: Optional[datetime] = Field(None, description="bio解析完成时间")
    bio_extracted_email: Optional[str] = Field(None, description="从bio提取的邮箱")
    ai_score: Optional[Decimal] = Field(None, description="AI评分结果", ge=0, le=100)
    ai_matched: Optional[bool] = Field(None, description="AI是否匹配")
    ai_scored_at: Optional[datetime] = Field(None, description="AI评分完成时间")
    nano_email_fetched_at: Optional[datetime] = Field(None, description="nano接口调用完成时间")
    nano_extracted_email: Optional[str] = Field(None, description="nano获取的邮箱")
    email_fetch_status: Optional[str] = Field("PENDING", description="邮箱获取状态")

    @field_validator('email', 'bio_extracted_email', 'nano_extracted_email', mode='before')
    @classmethod
    def validate_email(cls, v):
        """验证邮箱字段，允许空字符串转换为None"""
        if v == '' or v is None:
            return None
        # 简单的邮箱格式验证
        if isinstance(v, str) and '@' in v and '.' in v:
            return v
        return None


class KOLCreate(KOLBase):
    """创建KOL schema"""
    social_id: str = Field(..., description="KOL的唯一标识符", max_length=255)


class KOLUpdate(BaseModel):
    """更新KOL schema"""
    nick_name: Optional[str] = Field(None, description="KOL昵称", max_length=255)
    email: Optional[str] = Field(None, description="KOL邮箱")
    bio: Optional[str] = Field(None, description="KOL简介")
    followers_count: Optional[int] = Field(None, description="粉丝数", ge=0)
    likes_count: Optional[int] = Field(None, description="点赞数", ge=0)
    source: Optional[str] = Field(None, description="KOL来源", max_length=50)
    engagement_rate: Optional[Decimal] = Field(None, description="互动率", ge=0, le=1)
    mean_views_k: Optional[Decimal] = Field(None, description="平均观看数（千次）", ge=0)
    median_views_k: Optional[Decimal] = Field(None, description="中位观看数（千次）", ge=0)
    tier: Optional[KOLTierEnum] = Field(None, description="KOL分级")
    hashtags: Optional[List[str]] = Field(None, description="hashtag数组")
    captions: Optional[List[str]] = Field(None, description="caption数组")
    topics: Optional[List[str]] = Field(None, description="topic数组")
    note: Optional[str] = Field(None, description="备注")

    # Email获取状态相关字段
    bio_parsed_at: Optional[datetime] = Field(None, description="bio解析完成时间")
    bio_extracted_email: Optional[str] = Field(None, description="从bio提取的邮箱")
    ai_score: Optional[Decimal] = Field(None, description="AI评分结果", ge=0, le=100)
    ai_matched: Optional[bool] = Field(None, description="AI是否匹配")
    ai_scored_at: Optional[datetime] = Field(None, description="AI评分完成时间")
    nano_email_fetched_at: Optional[datetime] = Field(None, description="nano接口调用完成时间")
    nano_extracted_email: Optional[str] = Field(None, description="nano获取的邮箱")
    email_fetch_status: Optional[str] = Field(None, description="邮箱获取状态")


class KOLInDBBase(KOLBase):
    """数据库中的KOL基础schema"""
    id: int
    social_id: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class KOL(KOLInDBBase):
    """返回给客户端的KOL schema"""
    pass


class KOLInDB(KOLInDBBase):
    """数据库中的KOL schema（内部使用）"""
    pass


# KOL搜索
class KOLSearch(BaseModel):
    """KOL搜索schema"""
    # 基础信息搜索
    kol_id: Optional[int] = Field(None, description="KOL主键ID", ge=1)
    nick_name: Optional[str] = Field(None, description="昵称（模糊匹配）")
    social_id: Optional[str] = Field(None, description="社交媒体标识符（模糊匹配）")
    platform: Optional[PlatformEnum] = Field(None, description="平台")
    tier: Optional[KOLTierEnum] = Field(None, description="分级")
    source: Optional[str] = Field(None, description="KOL来源")
    project_code: Optional[str] = Field(None, description="项目编码")
    crawler_task_id: Optional[int] = Field(None, description="爬虫任务ID")

    # 数值范围搜索
    min_followers: Optional[int] = Field(None, description="最小粉丝数", ge=0)
    max_followers: Optional[int] = Field(None, description="最大粉丝数", ge=0)
    min_engagement_rate: Optional[Decimal] = Field(None, description="最小互动率", ge=0, le=1)
    max_engagement_rate: Optional[Decimal] = Field(None, description="最大互动率", ge=0, le=1)
    min_mean_views_k: Optional[Decimal] = Field(None, description="最小平均观看数（千次）", ge=0)
    max_mean_views_k: Optional[Decimal] = Field(None, description="最大平均观看数（千次）", ge=0)
    min_median_views_k: Optional[Decimal] = Field(None, description="最小中位观看数（千次）", ge=0)
    max_median_views_k: Optional[Decimal] = Field(None, description="最大中位观看数（千次）", ge=0)
    min_ai_score: Optional[Decimal] = Field(None, description="最小AI评分", ge=0, le=100)
    max_ai_score: Optional[Decimal] = Field(None, description="最大AI评分", ge=0, le=100)

    # 时间范围搜索
    created_after: Optional[datetime] = Field(None, description="创建时间起始（包含）")
    created_before: Optional[datetime] = Field(None, description="创建时间结束（包含）")
    updated_after: Optional[datetime] = Field(None, description="更新时间起始（包含）")
    updated_before: Optional[datetime] = Field(None, description="更新时间结束（包含）")

    # AI相关搜索
    ai_matched: Optional[bool] = Field(None, description="AI是否匹配")

    # 邮箱相关搜索
    email: Optional[str] = Field(None, description="邮箱地址（模糊匹配）")
    email_fetch_status: Optional[str] = Field(None, description="邮箱获取状态")
    has_email: Optional[bool] = Field(None, description="是否有邮箱")
    has_bio_extracted_email: Optional[bool] = Field(None, description="是否有从bio提取的邮箱")
    has_nano_extracted_email: Optional[bool] = Field(None, description="是否有nano获取的邮箱")

    # 新增：nano解锁和mismatch状态搜索
    is_nano_unlocked: Optional[bool] = Field(None, description="是否nano解锁过（基于nano_email_fetched_at字段）")
    is_ai_mismatched: Optional[bool] = Field(None, description="是否AI mismatch过（基于ai_scored_at字段）")

    # 内容标签搜索
    hashtags: Optional[List[str]] = Field(None, description="包含的hashtags（精确匹配）")
    topics: Optional[List[str]] = Field(None, description="包含的topics（精确匹配）")
    captions: Optional[List[str]] = Field(None, description="包含的captions（精确匹配）")

    # 内容模糊搜索 - 支持多关键词
    hashtags_fuzzy: Optional[List[str]] = Field(None, description="hashtags多模糊搜索关键词列表")
    topics_fuzzy: Optional[List[str]] = Field(None, description="topics多模糊搜索关键词列表")
    captions_fuzzy: Optional[List[str]] = Field(None, description="captions多模糊搜索关键词列表")


# KOL列表响应
class KOLListResponse(BaseModel):
    """KOL列表响应schema"""
    items: list[KOL]
    total: int
    skip: int
    limit: int


# KOL统计响应
class KOLStatsResponse(BaseModel):
    """KOL统计响应schema"""
    total: int
    with_email: int
    without_email: int
    platform_stats: dict
    tier_stats: dict
