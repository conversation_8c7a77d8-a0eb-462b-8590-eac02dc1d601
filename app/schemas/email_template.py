"""
邮件模板相关的Pydantic schemas
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, EmailStr


class EmailTemplateBase(BaseModel):
    """邮件模板基础schema"""
    name: str = Field(..., description="模板名称", max_length=255)
    project_code: str = Field(..., description="项目编码", max_length=50)
    postmark_token: Optional[str] = Field(None, description="邮件服务提供商的令牌", max_length=255)
    from_email: Optional[EmailStr] = Field(None, description="发件人邮箱")
    note: Optional[str] = Field(None, description="备注")


class EmailTemplateCreate(EmailTemplateBase):
    """创建邮件模板schema"""
    code: str = Field(..., description="模板唯一代码", max_length=50)


class EmailTemplateUpdate(BaseModel):
    """更新邮件模板schema"""
    name: Optional[str] = Field(None, description="模板名称", max_length=255)
    postmark_token: Optional[str] = Field(None, description="邮件服务提供商的令牌", max_length=255)
    from_email: Optional[EmailStr] = Field(None, description="发件人邮箱")
    note: Optional[str] = Field(None, description="备注")


class EmailTemplateInDBBase(EmailTemplateBase):
    """数据库中的邮件模板基础schema"""
    id: int
    code: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EmailTemplate(EmailTemplateInDBBase):
    """返回给客户端的邮件模板schema"""
    pass


class EmailTemplateInDB(EmailTemplateInDBBase):
    """数据库中的邮件模板schema（内部使用）"""
    pass


class EmailTemplateWithUsage(EmailTemplate):
    """包含使用统计的邮件模板schema"""
    usage_count: int = Field(..., description="使用次数")


# 邮件模板列表响应
class EmailTemplateListResponse(BaseModel):
    """邮件模板列表响应schema"""
    items: list[EmailTemplate]
    total: int
    skip: int
    limit: int


# 邮件模板使用统计响应
class EmailTemplateUsageResponse(BaseModel):
    """邮件模板使用统计响应schema"""
    items: list[EmailTemplateWithUsage]
    total: int
