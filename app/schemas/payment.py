"""
支付相关的Pydantic schemas
"""
from datetime import datetime
from typing import Optional
from decimal import Decimal
from pydantic import BaseModel, Field, EmailStr


class PaymentBase(BaseModel):
    """支付基础schema"""
    post_link: str = Field(..., description="帖子链接")
    payment_amount: Optional[Decimal] = Field(None, description="支付金额", ge=0)
    paypal_accounts: Optional[EmailStr] = Field(None, description="PayPal账户")
    tracker: Optional[str] = Field(None, description="跟进人", max_length=50)
    fund_source: Optional[str] = Field(None, description="资金来源", max_length=100)
    payment_screenshot_filename: Optional[str] = Field(None, description="支付截图文件名", max_length=255)
    note: Optional[str] = Field(None, description="备注")


class PaymentCreate(PaymentBase):
    """创建支付schema"""
    pass


class PaymentCreateWithFile(BaseModel):
    """创建支付schema（支持文件上传）"""
    post_link: str = Field(..., description="帖子链接")
    payment_amount: Optional[Decimal] = Field(None, description="支付金额", ge=0)
    paypal_accounts: Optional[str] = Field(None, description="PayPal账户")  # 改为str以支持表单数据
    tracker: Optional[str] = Field(None, description="跟进人", max_length=50)
    fund_source: Optional[str] = Field(None, description="资金来源", max_length=100)
    note: Optional[str] = Field(None, description="备注")


class PaymentCreateInternal(BaseModel):
    """内部创建支付schema（使用performance_id）"""
    performance_id: int = Field(..., description="绩效记录ID")
    payment_amount: Optional[Decimal] = Field(None, description="支付金额", ge=0)
    paypal_accounts: Optional[EmailStr] = Field(None, description="PayPal账户")
    tracker: Optional[str] = Field(None, description="跟进人", max_length=50)
    payout_date: Optional[datetime] = Field(None, description="支付日期")
    fund_source: Optional[str] = Field(None, description="资金来源", max_length=100)
    payment_screenshot_filename: Optional[str] = Field(None, description="支付截图文件名", max_length=255)
    note: Optional[str] = Field(None, description="备注")


class PaymentUpdate(BaseModel):
    """更新支付schema"""
    payment_amount: Optional[Decimal] = Field(None, description="支付金额", ge=0)
    paypal_accounts: Optional[EmailStr] = Field(None, description="PayPal账户")
    tracker: Optional[str] = Field(None, description="跟进人", max_length=50)
    payout_date: Optional[datetime] = Field(None, description="支付日期")
    fund_source: Optional[str] = Field(None, description="资金来源", max_length=100)
    payment_screenshot_filename: Optional[str] = Field(None, description="支付截图文件名", max_length=255)
    note: Optional[str] = Field(None, description="备注")


class PaymentInDBBase(BaseModel):
    """数据库中的支付基础schema"""
    id: int
    performance_id: int = Field(..., description="绩效记录ID")
    payment_amount: Optional[Decimal] = Field(None, description="支付金额", ge=0)
    paypal_accounts: Optional[EmailStr] = Field(None, description="PayPal账户")
    tracker: Optional[str] = Field(None, description="跟进人", max_length=50)
    fund_source: Optional[str] = Field(None, description="资金来源", max_length=100)
    payment_screenshot_filename: Optional[str] = Field(None, description="支付截图文件名", max_length=255)
    note: Optional[str] = Field(None, description="备注")
    payout_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Payment(PaymentBase):
    """返回给客户端的支付schema（使用 post_link）"""
    id: int
    payout_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime


class PaymentInDB(PaymentInDBBase):
    """数据库中的支付schema（内部使用）"""
    pass


# 标记为已支付
class PaymentMarkAsPaid(BaseModel):
    """标记为已支付schema"""
    payout_date: Optional[datetime] = Field(None, description="支付日期，不提供则使用当前时间")


# 支付搜索
class PaymentSearch(BaseModel):
    """支付搜索schema"""
    performance_id: Optional[int] = Field(None, description="绩效记录ID")
    tracker: Optional[str] = Field(None, description="跟进人（模糊匹配）")
    paypal_account: Optional[str] = Field(None, description="PayPal账户（模糊匹配）")
    fund_source: Optional[str] = Field(None, description="资金来源（模糊匹配）")
    min_amount: Optional[Decimal] = Field(None, description="最小金额", ge=0)
    max_amount: Optional[Decimal] = Field(None, description="最大金额", ge=0)
    has_payout_date: Optional[bool] = Field(None, description="是否已支付")


# 支付列表响应
class PaymentListResponse(BaseModel):
    """支付列表响应schema"""
    items: list[Payment]
    total: int
    skip: int
    limit: int


# 支付统计响应
class PaymentStatsResponse(BaseModel):
    """支付统计响应schema"""
    total: int
    completed: int
    pending: int
    completion_rate: float
    amount_stats: dict
    paid_stats: dict
    tracker_stats: dict
