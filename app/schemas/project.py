"""
项目相关的Pydantic schemas
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class ProjectBase(BaseModel):
    """项目基础schema"""
    name: str = Field(..., description="项目名称", max_length=255)
    description: Optional[str] = Field(None, description="项目描述")


class ProjectCreate(ProjectBase):
    """创建项目schema"""
    code: str = Field(..., description="项目唯一标识符", max_length=50)


class ProjectUpdate(BaseModel):
    """更新项目schema"""
    name: Optional[str] = Field(None, description="项目名称", max_length=255)
    description: Optional[str] = Field(None, description="项目描述")


class ProjectInDBBase(ProjectBase):
    """数据库中的项目基础schema"""
    code: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Project(ProjectInDBBase):
    """返回给客户端的项目schema"""
    pass


class ProjectInDB(ProjectInDBBase):
    """数据库中的项目schema（内部使用）"""
    pass



# 项目列表响应
class ProjectListResponse(BaseModel):
    """项目列表响应schema"""
    items: list[Project]
    total: int
    skip: int
    limit: int

