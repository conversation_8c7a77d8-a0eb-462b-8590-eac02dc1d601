"""
KOL表现视图相关的Pydantic schemas
"""
from datetime import datetime, date
from typing import Optional, List
from decimal import Decimal
from pydantic import BaseModel, Field

from app.models.enums import PlatformEnum


class KOLPerformanceView(BaseModel):
    """KOL表现视图schema"""
    # 主键ID字段
    performance_id: int = Field(..., description="绩效记录ID")
    payment_id: Optional[int] = Field(None, description="支付记录ID")
    # KOL基础信息
    platform: PlatformEnum = Field(..., description="KOL所在平台")
    social_id: str = Field(..., description="KOL的社交媒体ID")
    nick_name: str = Field(..., description="KOL昵称")
    project_code: str = Field(..., description="项目编码")
    post_link: str = Field(..., description="帖子链接")
    cpm: Optional[Decimal] = Field(None, description="每千次曝光费用")
    engagement_rate: Optional[Decimal] = Field(None, description="互动率 (点赞+评论+分享)/观看数")
    status: str = Field(..., description="绩效状态")
    views_total: Optional[int] = Field(None, description="总观看数")
    likes_total: Optional[int] = Field(None, description="总点赞数")
    comments_total: Optional[int] = Field(None, description="总评论数")
    shares_total: Optional[int] = Field(None, description="总分享数")
    post_date: Optional[datetime] = Field(None, description="发布日期")
    payment_amount: Optional[Decimal] = Field(None, description="支付金额")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class KOLPerformanceSearch(BaseModel):
    """KOL表现搜索schema - 支持多种查询条件"""

    # 基础筛选
    platform: Optional[PlatformEnum] = Field(None, description="平台筛选")
    platforms: Optional[List[PlatformEnum]] = Field(None, description="多平台筛选")
    project_code: Optional[str] = Field(None, description="项目编码精确匹配")
    project_codes: Optional[List[str]] = Field(None, description="多项目编码筛选")
    status: Optional[str] = Field(None, description="绩效状态")
    statuses: Optional[List[str]] = Field(None, description="多状态筛选")

    # KOL信息搜索
    social_id: Optional[str] = Field(None, description="KOL社交媒体ID精确匹配")
    social_id_like: Optional[str] = Field(None, description="KOL社交媒体ID模糊搜索")
    nick_name: Optional[str] = Field(None, description="KOL昵称精确匹配")
    nick_name_like: Optional[str] = Field(None, description="KOL昵称模糊搜索")

    # 帖子信息搜索
    post_link: Optional[str] = Field(None, description="帖子链接精确匹配")
    post_link_like: Optional[str] = Field(None, description="帖子链接模糊搜索")

    # 数值范围筛选
    min_cpm: Optional[Decimal] = Field(None, description="最小CPM", ge=0)
    max_cpm: Optional[Decimal] = Field(None, description="最大CPM", ge=0)
    min_views: Optional[int] = Field(None, description="最小观看数", ge=0)
    max_views: Optional[int] = Field(None, description="最大观看数", ge=0)
    min_likes: Optional[int] = Field(None, description="最小点赞数", ge=0)
    max_likes: Optional[int] = Field(None, description="最大点赞数", ge=0)
    min_comments: Optional[int] = Field(None, description="最小评论数", ge=0)
    max_comments: Optional[int] = Field(None, description="最大评论数", ge=0)
    min_shares: Optional[int] = Field(None, description="最小分享数", ge=0)
    max_shares: Optional[int] = Field(None, description="最大分享数", ge=0)
    min_engagement_rate: Optional[Decimal] = Field(None, description="最小互动率", ge=0, le=1)
    max_engagement_rate: Optional[Decimal] = Field(None, description="最大互动率", ge=0, le=1)
    min_payment_amount: Optional[Decimal] = Field(None, description="最小支付金额", ge=0)
    max_payment_amount: Optional[Decimal] = Field(None, description="最大支付金额", ge=0)

    # 日期范围筛选
    post_date_start: Optional[date] = Field(None, description="发布日期开始")
    post_date_end: Optional[date] = Field(None, description="发布日期结束")
    created_at_start: Optional[datetime] = Field(None, description="创建时间开始")
    created_at_end: Optional[datetime] = Field(None, description="创建时间结束")

    # 排序选项
    sort_by: Optional[str] = Field("created_at", description="排序字段")
    sort_order: Optional[str] = Field("desc", description="排序方向")

    # 特殊筛选
    has_payment: Optional[bool] = Field(None, description="是否有支付记录")
    has_engagement_data: Optional[bool] = Field(None, description="是否有互动数据")

    class Config:
        json_schema_extra = {
            "example": {
                "social_id_like": "evan",
                "nick_name_like": "测试",
                "project_codes": ["OOG120", "OOG121"],
                "platforms": ["TIKTOK", "INSTAGRAM"],
                "min_views": 1000,
                "max_views": 100000,
                "min_engagement_rate": 0.01,
                "max_engagement_rate": 0.1,
                "post_date_start": "2025-01-01",
                "post_date_end": "2025-12-31",
                "sort_by": "engagement_rate",
                "sort_order": "desc",
                "has_payment": True
            }
        }


class KOLPerformanceListResponse(BaseModel):
    """KOL表现列表响应schema"""
    items: list[KOLPerformanceView]
    total: int
    skip: int
    limit: int
