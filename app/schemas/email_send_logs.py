"""
邮件发送相关的Pydantic schemas
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr

from app.models.enums import PlatformEnum, EmailSendStatusEnum


class EmailSendBase(BaseModel):
    """邮件发送基础schema"""
    platform: Optional[PlatformEnum] = Field(None, description="邮件发送的平台")
    social_id: str = Field(..., description="KOL社交媒体ID")
    project_code: str = Field(..., description="项目编码", max_length=50)
    template_code: str = Field(..., description="邮件模板代码", max_length=50)
    from_email: Optional[EmailStr] = Field(None, description="发件人邮箱")
    to_email: Optional[EmailStr] = Field(None, description="收件人邮箱")
    note: Optional[str] = Field(None, description="备注")


class EmailSendCreate(EmailSendBase):
    """创建邮件发送schema"""
    pass


class EmailSendCreateInternal(BaseModel):
    """内部创建邮件发送schema（使用kol_id）"""
    platform: Optional[PlatformEnum] = Field(None, description="邮件发送的平台")
    kol_id: int = Field(..., description="KOL ID")
    project_code: str = Field(..., description="项目编码", max_length=50)
    template_code: str = Field(..., description="邮件模板代码", max_length=50)
    from_email: Optional[EmailStr] = Field(None, description="发件人邮箱")
    to_email: Optional[EmailStr] = Field(None, description="收件人邮箱")
    note: Optional[str] = Field(None, description="备注")


class EmailSendUpdate(BaseModel):
    """更新邮件发送schema"""
    send_status: Optional[EmailSendStatusEnum] = Field(None, description="邮件发送状态")
    send_date: Optional[datetime] = Field(None, description="邮件发送时间")
    from_email: Optional[EmailStr] = Field(None, description="发件人邮箱")
    to_email: Optional[EmailStr] = Field(None, description="收件人邮箱")
    note: Optional[str] = Field(None, description="备注")


class EmailSendInDBBase(BaseModel):
    """数据库中的邮件发送基础schema"""
    id: int
    platform: Optional[PlatformEnum] = Field(None, description="邮件发送的平台")
    kol_id: int = Field(..., description="KOL ID")
    project_code: str = Field(..., description="项目编码", max_length=50)
    template_code: str = Field(..., description="邮件模板代码", max_length=50)
    from_email: Optional[EmailStr] = Field(None, description="发件人邮箱")
    to_email: Optional[EmailStr] = Field(None, description="收件人邮箱")
    note: Optional[str] = Field(None, description="备注")
    send_status: EmailSendStatusEnum
    send_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EmailSend(EmailSendBase):
    """返回给客户端的邮件发送schema（使用 social_id）"""
    id: int
    send_status: EmailSendStatusEnum
    send_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime


class EmailSendInDB(EmailSendInDBBase):
    """数据库中的邮件发送schema（使用 kol_id）"""
    pass


# 邮件发送状态更新
class EmailSendStatusUpdate(BaseModel):
    """邮件发送状态更新schema"""
    status: EmailSendStatusEnum = Field(..., description="新状态")
    send_date: Optional[datetime] = Field(None, description="发送时间")


# 邮件发送搜索
class EmailSendSearch(BaseModel):
    """邮件发送搜索schema"""
    social_id: Optional[str] = Field(None, description="KOL社交媒体ID")
    platform: Optional[PlatformEnum] = Field(None, description="平台")
    status: Optional[EmailSendStatusEnum] = Field(None, description="发送状态")
    template_code: Optional[str] = Field(None, description="模板代码")
    project_code: Optional[str] = Field(None, description="项目编码")
    from_email: Optional[str] = Field(None, description="发件人邮箱（模糊匹配）")
    to_email: Optional[str] = Field(None, description="收件人邮箱（模糊匹配）")


# 邮件发送列表响应
class EmailSendListResponse(BaseModel):
    """邮件发送列表响应schema"""
    items: list[EmailSend]
    total: int
    skip: int
    limit: int


# 邮件发送统计响应
class EmailSendStatsResponse(BaseModel):
    """邮件发送统计响应schema"""
    total: int
    pending: int
    sent: int
    failed: int
    success_rate: float
    platform_stats: dict


# 批量邮件发送相关schemas
class KOLEmailData(BaseModel):
    """KOL邮件数据schema"""
    social_id: str = Field(..., description="KOL社交媒体ID")
    platform: str = Field(..., description="平台名称")
    email: EmailStr = Field(..., description="邮箱地址")
    project_code: str = Field(..., description="项目编码")
    template_code: str = Field(..., description="邮件模板代码")
    name: Optional[str] = Field(None, description="KOL姓名")
    nick_name: Optional[str] = Field(None, description="KOL昵称")
    account_link: Optional[str] = Field(None, description="账户链接")


class BatchEmailSendRequest(BaseModel):
    """批量邮件发送请求schema"""
    email_data_list: List[KOLEmailData] = Field(..., description="邮件数据列表")


class KOLIdentifier(BaseModel):
    """KOL标识符"""
    social_id: str = Field(..., description="KOL社交媒体ID")
    platform: PlatformEnum = Field(..., description="平台")
    project_code: str = Field(..., description="项目编码")


class BatchEmailSendByKOLsRequest(BaseModel):
    """向指定KOL批量发送邮件请求schema"""
    kols: List[KOLIdentifier] = Field(..., description="KOL标识符列表")
    template_code: str = Field(..., description="邮件模板代码")


class BatchEmailSendByProjectRequest(BaseModel):
    """向项目KOL批量发送邮件请求schema"""
    project_code: str = Field(..., description="项目编码")
    template_code: str = Field(..., description="邮件模板代码")
    platform: Optional[PlatformEnum] = Field(None, description="平台过滤")
    limit: int = Field(100, ge=1, le=1000, description="发送数量限制")


class EmailSendResult(BaseModel):
    """邮件发送结果schema"""
    successful_emails_num: int = Field(..., description="成功发送数量")
    failed_emails_num: int = Field(..., description="失败发送数量")
    total_emails_num: int = Field(..., description="总发送数量")
    message: Optional[str] = Field(None, description="结果消息")


class PlatformEmailResult(BaseModel):
    """平台邮件发送结果schema"""
    successful_emails: List[str] = Field(..., description="成功发送的邮箱列表")
    failed_emails: List[str] = Field(..., description="失败发送的邮箱列表")
    stats: EmailSendResult = Field(..., description="统计信息")


class BatchEmailSendResponse(EmailSendResult):
    """批量邮件发送响应schema"""
    platform_results: Optional[Dict[str, PlatformEmailResult]] = Field(None, description="各平台发送结果")


class EmailCategorizeRequest(BaseModel):
    """邮件分类请求schema"""
    email_data_list: List[KOLEmailData] = Field(..., description="邮件数据列表")


class CategorizedEmails(BaseModel):
    """分类后的邮件schema"""
    tiktok: Dict[str, List[KOLEmailData]] = Field(default_factory=dict, description="TikTok平台邮件")
    instagram: Dict[str, List[KOLEmailData]] = Field(default_factory=dict, description="Instagram平台邮件")
    youtube: Dict[str, List[KOLEmailData]] = Field(default_factory=dict, description="YouTube平台邮件")


class EmailCategorizeResponse(BaseModel):
    """邮件分类响应schema"""
    categorized_emails: CategorizedEmails = Field(..., description="分类后的邮件")
    empty_status_emails: List[str] = Field(..., description="空状态邮件列表")
    total_count: int = Field(..., description="总邮件数量")
    categorized_count: int = Field(..., description="成功分类数量")


class PendingEmailsRequest(BaseModel):
    """获取待发送邮件请求schema"""
    project_code: Optional[str] = Field(None, description="项目编码过滤")
    platform: Optional[PlatformEnum] = Field(None, description="平台过滤")
    limit: int = Field(100, ge=1, le=1000, description="限制数量")


class PendingEmailsResponse(BaseModel):
    """待发送邮件响应schema"""
    email_data_list: List[KOLEmailData] = Field(..., description="待发送邮件数据列表")
    total_count: int = Field(..., description="总数量")
