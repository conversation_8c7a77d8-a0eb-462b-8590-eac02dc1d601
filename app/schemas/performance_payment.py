"""
绩效和支付统一管理的Pydantic schemas
"""
from datetime import datetime
from typing import Optional
from decimal import Decimal
from pydantic import BaseModel, Field

from app.models.enums import PlatformEnum


class PerformancePaymentCreate(BaseModel):
    """统一创建绩效和支付记录的schema"""
    # 绩效相关字段
    platform: PlatformEnum = Field(..., description="KOL所在平台")
    social_id: str = Field(..., description="KOL的社交媒体ID", max_length=255)
    project_code: str = Field(..., description="项目编码", max_length=50)
    post_link: str = Field(..., description="帖子链接", max_length=500)
    post_date: Optional[datetime] = Field(None, description="发布日期")

    # 支付相关字段
    payment_amount: Optional[Decimal] = Field(None, description="支付金额", ge=0)
    paypal_accounts: Optional[str] = Field(None, description="PayPal账户")
    tracker: Optional[str] = Field(None, description="跟进人", max_length=50)
    payout_date: Optional[datetime] = Field(None, description="支付日期")
    fund_source: Optional[str] = Field(None, description="资金来源", max_length=100)
    note: Optional[str] = Field(None, description="备注")


class PerformancePaymentResponse(BaseModel):
    """统一创建绩效和支付记录的响应schema"""
    # 绩效信息
    performance_id: int = Field(..., description="绩效记录ID")
    platform: PlatformEnum = Field(..., description="KOL所在平台")
    social_id: str = Field(..., description="KOL的社交媒体ID")
    kol_id: int = Field(..., description="KOL ID")
    project_code: str = Field(..., description="项目编码")
    post_link: str = Field(..., description="帖子链接")
    post_date: Optional[datetime] = Field(None, description="发布日期")
    performance_created_at: datetime = Field(..., description="绩效记录创建时间")
    
    # 支付信息
    payment_id: int = Field(..., description="支付记录ID")
    payment_amount: Optional[Decimal] = Field(None, description="支付金额")
    paypal_accounts: Optional[str] = Field(None, description="PayPal账户")
    tracker: Optional[str] = Field(None, description="跟进人")
    payout_date: Optional[datetime] = Field(None, description="支付日期")
    fund_source: Optional[str] = Field(None, description="资金来源")
    payment_screenshot_filename: Optional[str] = Field(None, description="支付截图文件名")
    payment_screenshot_path: Optional[str] = Field(None, description="支付截图完整路径")
    note: Optional[str] = Field(None, description="备注")
    payment_created_at: datetime = Field(..., description="支付记录创建时间")
    
    class Config:
        from_attributes = True
