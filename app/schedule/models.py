"""
定时任务相关数据模型
"""
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 运行中
    SUCCESS = "success"      # 执行成功
    FAILED = "failed"        # 执行失败
    TIMEOUT = "timeout"      # 执行超时
    CANCELLED = "cancelled"  # 已取消


@dataclass
class TaskConfig:
    """任务配置"""
    task_id: str
    cron_expr: str = "0 22 * * *"  # 默认每晚10点
    timeout_minutes: int = 30       # 默认超时30分钟
    max_retries: int = 1           # 默认重试1次
    retry_interval_minutes: int = 5 # 重试间隔5分钟
    enabled: bool = True           # 是否启用
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'cron_expr': self.cron_expr,
            'timeout_minutes': self.timeout_minutes,
            'max_retries': self.max_retries,
            'retry_interval_minutes': self.retry_interval_minutes,
            'enabled': self.enabled
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskConfig':
        """从字典创建配置对象"""
        return cls(**data)


@dataclass
class TaskExecution:
    """任务执行记录"""
    task_id: str
    execution_id: str
    status: TaskStatus
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'execution_id': self.execution_id,
            'status': self.status.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }
