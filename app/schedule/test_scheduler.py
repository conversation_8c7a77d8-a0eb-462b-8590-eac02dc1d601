"""
定时任务框架测试
简单的功能测试，验证核心功能是否正常工作
"""
import time
import logging
from datetime import datetime
import unittest
from unittest.mock import Mock, patch

from app.schedule import TaskScheduler, TaskConfig, TaskStatus
from app.schedule.api import ScheduleAPI, init_schedule_api
from app.schedule.utils import validate_cron_expression, get_common_cron_expressions


class MockTask:
    """模拟任务类，用于测试"""
    
    def __init__(self, name: str, should_fail: bool = False, sleep_time: float = 0.1):
        self.name = name
        self.should_fail = should_fail
        self.sleep_time = sleep_time
        self.run_count = 0
    
    def run(self):
        """模拟任务执行"""
        self.run_count += 1
        time.sleep(self.sleep_time)
        
        if self.should_fail:
            raise Exception(f"Mock task {self.name} failed intentionally")
        
        return f"Mock task {self.name} completed (run #{self.run_count})"


class TestTaskScheduler(unittest.TestCase):
    """任务调度器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.scheduler = TaskScheduler()
        self.test_task = MockTask("test_task")
    
    def tearDown(self):
        """测试后清理"""
        if self.scheduler.scheduler.running:
            self.scheduler.shutdown(wait=False)
    
    def test_scheduler_initialization(self):
        """测试调度器初始化"""
        self.assertIsNotNone(self.scheduler)
        self.assertFalse(self.scheduler.scheduler.running)
        self.assertEqual(len(self.scheduler.task_configs), 0)
    
    def test_task_registration(self):
        """测试任务注册"""
        config = TaskConfig(task_id="test_task", cron_expr="* * * * *")
        self.scheduler.register_task("test_task", self.test_task, config)
        
        self.assertIn("test_task", self.scheduler.task_configs)
        self.assertIn("test_task", self.scheduler.task_functions)
        self.assertEqual(self.scheduler.task_configs["test_task"].cron_expr, "* * * * *")
    
    def test_scheduler_start_stop(self):
        """测试调度器启动和停止"""
        self.scheduler.start()
        self.assertTrue(self.scheduler.scheduler.running)
        
        self.scheduler.shutdown()
        self.assertFalse(self.scheduler.scheduler.running)
    
    def test_cron_update(self):
        """测试cron表达式更新"""
        config = TaskConfig(task_id="test_task", cron_expr="0 0 * * *")
        self.scheduler.register_task("test_task", self.test_task, config)
        
        # 更新cron表达式
        result = self.scheduler.set_task_cron("test_task", "0 12 * * *")
        self.assertTrue(result)
        self.assertEqual(self.scheduler.task_configs["test_task"].cron_expr, "0 12 * * *")
    
    def test_task_removal(self):
        """测试任务移除"""
        config = TaskConfig(task_id="test_task")
        self.scheduler.register_task("test_task", self.test_task, config)
        
        # 移除任务
        result = self.scheduler.remove_task("test_task")
        self.assertTrue(result)
        self.assertNotIn("test_task", self.scheduler.task_configs)
        self.assertNotIn("test_task", self.scheduler.task_functions)
    
    def test_task_status(self):
        """测试任务状态获取"""
        config = TaskConfig(task_id="test_task")
        self.scheduler.register_task("test_task", self.test_task, config)
        
        status = self.scheduler.get_task_status("test_task")
        self.assertIsNotNone(status)
        self.assertEqual(status["task_id"], "test_task")
        self.assertIn("config", status)
        self.assertIn("is_running", status)


class TestScheduleAPI(unittest.TestCase):
    """调度API测试"""
    
    def setUp(self):
        """测试前准备"""
        self.scheduler = TaskScheduler()
        self.api = ScheduleAPI(self.scheduler)
        self.test_task = MockTask("api_test_task")
        
        # 注册测试任务
        config = TaskConfig(task_id="api_test_task")
        self.scheduler.register_task("api_test_task", self.test_task, config)
    
    def tearDown(self):
        """测试后清理"""
        if self.scheduler.scheduler.running:
            self.scheduler.shutdown(wait=False)
    
    def test_update_task_schedule(self):
        """测试更新任务调度"""
        result = self.api.update_task_schedule("api_test_task", "0 9 * * *")
        
        self.assertTrue(result["success"])
        self.assertIn("更新成功", result["message"])
        self.assertIsNotNone(result["data"])
    
    def test_update_nonexistent_task(self):
        """测试更新不存在的任务"""
        result = self.api.update_task_schedule("nonexistent", "0 9 * * *")
        
        self.assertFalse(result["success"])
        self.assertIn("更新失败", result["message"])
    
    def test_get_task_info(self):
        """测试获取任务信息"""
        result = self.api.get_task_info("api_test_task")
        
        self.assertTrue(result["success"])
        self.assertIsNotNone(result["data"])
        self.assertEqual(result["data"]["task_id"], "api_test_task")
    
    def test_list_all_tasks(self):
        """测试列出所有任务"""
        result = self.api.list_all_tasks()
        
        self.assertTrue(result["success"])
        self.assertIn("tasks", result["data"])
        self.assertEqual(result["data"]["total_count"], 1)
    
    def test_remove_task(self):
        """测试移除任务"""
        result = self.api.remove_task("api_test_task")
        
        self.assertTrue(result["success"])
        self.assertIn("移除成功", result["message"])
    
    def test_enable_disable_task(self):
        """测试启用/禁用任务"""
        # 禁用任务
        result = self.api.disable_task("api_test_task")
        self.assertTrue(result["success"])
        
        # 启用任务
        result = self.api.enable_task("api_test_task")
        self.assertTrue(result["success"])


class TestUtils(unittest.TestCase):
    """工具函数测试"""
    
    def test_validate_cron_expression(self):
        """测试cron表达式验证"""
        # 有效表达式
        result = validate_cron_expression("0 9 * * *")
        self.assertTrue(result["valid"])
        self.assertEqual(len(result["next_runs"]), 5)
        
        # 无效表达式
        result = validate_cron_expression("invalid cron")
        self.assertFalse(result["valid"])
        self.assertEqual(len(result["next_runs"]), 0)
    
    def test_get_common_cron_expressions(self):
        """测试获取常用cron表达式"""
        expressions = get_common_cron_expressions()
        
        self.assertIsInstance(expressions, dict)
        self.assertIn("每天晚上10点", expressions)
        self.assertEqual(expressions["每天晚上10点"], "0 22 * * *")


def run_integration_test():
    """集成测试：模拟真实使用场景"""
    print("\n=== 集成测试开始 ===")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("integration_test")
    
    try:
        # 1. 创建调度器和API
        scheduler = TaskScheduler()
        api = init_schedule_api(scheduler)
        
        # 2. 创建测试任务
        quick_task = MockTask("quick_task", sleep_time=0.5)
        
        # 3. 注册任务（每分钟执行一次，用于快速测试）
        config = TaskConfig(
            task_id="quick_task",
            cron_expr="* * * * *",  # 每分钟
            timeout_minutes=1,
            max_retries=1
        )
        scheduler.register_task("quick_task", quick_task, config)
        
        # 4. 启动调度器
        scheduler.start()
        logger.info("调度器已启动")
        
        # 5. 测试API调用
        logger.info("测试API调用...")
        
        # 获取任务信息
        result = api.get_task_info("quick_task")
        assert result["success"], "获取任务信息失败"
        logger.info("✓ 获取任务信息成功")
        
        # 更新调度（改为每30秒）
        result = api.update_task_schedule("quick_task", "*/30 * * * * *")
        assert result["success"], "更新调度失败"
        logger.info("✓ 更新调度成功")
        
        # 列出所有任务
        result = api.list_all_tasks()
        assert result["success"], "列出任务失败"
        assert result["data"]["total_count"] == 1, "任务数量不正确"
        logger.info("✓ 列出任务成功")
        
        # 6. 等待任务执行
        logger.info("等待任务执行...")
        time.sleep(35)  # 等待35秒，应该执行一次
        
        # 检查任务是否执行
        if quick_task.run_count > 0:
            logger.info("✓ 任务执行成功，执行次数: %d", quick_task.run_count)
        else:
            logger.warning("⚠ 任务未执行，可能需要更长等待时间")
        
        # 7. 移除任务
        result = api.remove_task("quick_task")
        assert result["success"], "移除任务失败"
        logger.info("✓ 移除任务成功")
        
        logger.info("=== 集成测试完成 ===")
        
    except Exception as e:
        logger.error("集成测试失败: %s", str(e))
        raise
    
    finally:
        # 清理
        scheduler.shutdown()
        logger.info("调度器已关闭")


if __name__ == "__main__":
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行集成测试
    print("\n" + "="*50)
    run_integration_test()
