"""
绩效数据同步定时任务配置
将PerformanceSyncTask集成到定时任务框架中
"""
import logging
from datetime import datetime

from app.schedule import TaskScheduler, TaskConfig
from app.schedule.api import init_schedule_api
from app.tasks.task import PerformanceSyncTask
from app.logging_config import get_task_logger

logger = get_task_logger("performance_sync_scheduler")


class PerformanceSyncTaskWrapper:
    """绩效数据同步任务包装器，适配定时任务框架"""
    
    def __init__(self, project_code: str = None, sync_type: str = "batch", batch_size: int = 50):
        self.project_code = project_code
        self.sync_type = sync_type
        self.batch_size = batch_size
        self.logger = get_task_logger(f"performance_sync_{sync_type}")
    
    def run(self):
        """执行绩效数据同步任务"""
        self.logger.info(f"开始执行绩效数据同步任务，类型: {self.sync_type}, 项目: {self.project_code}")
        
        try:
            # 创建PerformanceSyncTask实例
            shared_data = {
                "sync_type": self.sync_type,
                "batch_size": self.batch_size
            }
            
            # 如果指定了项目代码，添加到共享数据中
            if self.project_code:
                shared_data["project_code"] = self.project_code
            
            task = PerformanceSyncTask(shared_data)
            result = task.run()
            
            self.logger.info(f"绩效数据同步任务完成，类型: {self.sync_type}, 结果: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"绩效数据同步任务失败，类型: {self.sync_type}, 错误: {e}")
            raise


def setup_performance_sync_scheduler():
    """设置绩效数据同步定时任务"""
    
    # 创建调度器
    scheduler = TaskScheduler(timezone='Asia/Shanghai')
    
    # 初始化API
    api = init_schedule_api(scheduler)
    
    # 创建批量绩效数据同步任务
    batch_sync_task = PerformanceSyncTaskWrapper(
        sync_type="batch",
        batch_size=100  # 每次处理100条记录
    )
    
    # 配置任务 - 每天早上8点执行
    batch_config = TaskConfig(
        task_id="performance_sync_batch",
        cron_expr="0 8 * * *",  # 每天早上8点 (Daily at 08:00 AM Asia/Shanghai)
        timeout_minutes=120,     # 超时时间120分钟
        max_retries=3,          # 最大重试3次
        retry_interval_minutes=15,  # 重试间隔15分钟
        enabled=True
    )
    
    # 注册批量同步任务
    scheduler.register_task("performance_sync_batch", batch_sync_task, batch_config)
    
    # 可选：为特定项目创建单独的同步任务
    # 创建OOG120项目的绩效数据同步任务
    oog120_sync_task = PerformanceSyncTaskWrapper(
        project_code="OOG120",
        sync_type="batch",
        batch_size=50
    )
    
    # 配置OOG120项目任务 - 每天早上8:30执行
    oog120_config = TaskConfig(
        task_id="performance_sync_oog120",
        cron_expr="30 8 * * *",  # 每天早上8:30 (Daily at 08:30 AM Asia/Shanghai)
        timeout_minutes=60,      # 超时时间60分钟
        max_retries=2,          # 最大重试2次
        retry_interval_minutes=10,  # 重试间隔10分钟
        enabled=True
    )
    
    # 注册OOG120项目同步任务
    scheduler.register_task("performance_sync_oog120", oog120_sync_task, oog120_config)
    
    logger.info("绩效数据同步定时任务已配置:")
    logger.info("- 批量同步: 每天早上8:00执行")
    logger.info("- OOG120项目同步: 每天早上8:30执行")
    
    return scheduler, api


def setup_single_platform_sync_tasks(scheduler: TaskScheduler):
    """设置单平台同步任务（可选）"""
    
    # TikTok同步任务 - 每天早上9点执行
    tiktok_task = PerformanceSyncTaskWrapper(sync_type="tiktok", batch_size=30)
    tiktok_config = TaskConfig(
        task_id="performance_sync_tiktok",
        cron_expr="0 9 * * *",  # 每天早上9点
        timeout_minutes=60,
        max_retries=2,
        retry_interval_minutes=10,
        enabled=False  # 默认禁用，可根据需要启用
    )
    scheduler.register_task("performance_sync_tiktok", tiktok_task, tiktok_config)
    
    # Instagram同步任务 - 每天早上9:30执行
    instagram_task = PerformanceSyncTaskWrapper(sync_type="instagram", batch_size=30)
    instagram_config = TaskConfig(
        task_id="performance_sync_instagram",
        cron_expr="30 9 * * *",  # 每天早上9:30
        timeout_minutes=60,
        max_retries=2,
        retry_interval_minutes=10,
        enabled=False  # 默认禁用，可根据需要启用
    )
    scheduler.register_task("performance_sync_instagram", instagram_task, instagram_config)
    
    # YouTube同步任务 - 每天早上10点执行
    youtube_task = PerformanceSyncTaskWrapper(sync_type="youtube", batch_size=30)
    youtube_config = TaskConfig(
        task_id="performance_sync_youtube",
        cron_expr="0 10 * * *",  # 每天早上10点
        timeout_minutes=60,
        max_retries=2,
        retry_interval_minutes=10,
        enabled=False  # 默认禁用，可根据需要启用
    )
    scheduler.register_task("performance_sync_youtube", youtube_task, youtube_config)
    
    logger.info("单平台同步任务已配置（默认禁用）:")
    logger.info("- TikTok同步: 每天早上9:00")
    logger.info("- Instagram同步: 每天早上9:30")
    logger.info("- YouTube同步: 每天早上10:00")


def start_performance_sync_scheduler():
    """启动绩效数据同步定时任务调度器"""
    scheduler, api = setup_performance_sync_scheduler()
    
    # 可选：设置单平台同步任务
    setup_single_platform_sync_tasks(scheduler)
    
    try:
        # 启动调度器
        scheduler.start()
        logger.info("绩效数据同步定时任务调度器已启动")
        
        # 返回调度器和API供外部使用
        return scheduler, api
        
    except Exception as e:
        logger.error(f"启动绩效数据同步定时任务调度器失败: {e}")
        raise


def stop_performance_sync_scheduler(scheduler: TaskScheduler):
    """停止绩效数据同步定时任务调度器"""
    try:
        scheduler.shutdown()
        logger.info("绩效数据同步定时任务调度器已停止")
    except Exception as e:
        logger.error(f"停止绩效数据同步定时任务调度器失败: {e}")


if __name__ == "__main__":
    """测试运行"""
    import time
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    logger.info("=== 绩效数据同步定时任务测试 ===")
    
    # 启动调度器
    scheduler, api = start_performance_sync_scheduler()
    
    try:
        # 获取任务信息
        batch_task_info = api.get_task_info("performance_sync_batch")
        logger.info(f"批量同步任务信息: {batch_task_info}")
        
        oog120_task_info = api.get_task_info("performance_sync_oog120")
        logger.info(f"OOG120同步任务信息: {oog120_task_info}")
        
        # 运行一段时间进行测试
        logger.info("调度器将运行120秒进行测试...")
        time.sleep(120)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    
    finally:
        # 停止调度器
        stop_performance_sync_scheduler(scheduler)
