"""
Gmail同步定时任务配置
将SyncGmailTask集成到定时任务框架中
"""
import logging
from datetime import datetime

from app.schedule import TaskScheduler, TaskConfig
from app.schedule.api import init_schedule_api
from app.tasks.task import SyncGmailTask
from app.logging_config import get_task_logger

logger = get_task_logger("gmail_sync_scheduler")


class GmailSyncTaskWrapper:
    """Gmail同步任务包装器，适配定时任务框架"""

    def __init__(self, project_code: str, cron_expr: str):
        self.project_code = project_code
        self.cron_expr = cron_expr
        self.logger = get_task_logger(f"gmail_sync_{project_code}")

    def run(self):
        """执行Gmail同步任务"""
        self.logger.info(f"开始执行Gmail同步任务，项目: {self.project_code}, Cron: {self.cron_expr}")

        try:
            # 创建SyncGmailTask实例
            shared_data = {
                "project_code": self.project_code,
                "cron_expr": self.cron_expr
            }

            task = SyncGmailTask(shared_data)
            result = task.run()

            self.logger.info(f"Gmail同步任务完成，项目: {self.project_code}, 结果: {result}")
            return result

        except Exception as e:
            self.logger.error(f"Gmail同步任务失败，项目: {self.project_code}, 错误: {e}")
            raise


def setup_gmail_sync_scheduler(project_code: str = "OOG120", cron_expr: str = "0 20 * * *"):
    """设置Gmail同步定时任务

    Args:
        project_code: 项目编码
        cron_expr: Cron表达式
    """

    # 创建调度器
    scheduler = TaskScheduler(timezone='Asia/Shanghai')

    # 初始化API
    api = init_schedule_api(scheduler)

    # 创建Gmail同步任务
    gmail_sync_task = GmailSyncTaskWrapper(project_code, cron_expr)

    # 配置任务
    task_id = f"gmail_sync_{project_code.lower()}"
    config = TaskConfig(
        task_id=task_id,
        cron_expr=cron_expr,
        timeout_minutes=60,      # 超时时间60分钟
        max_retries=2,          # 最大重试2次
        retry_interval_minutes=10,  # 重试间隔10分钟
        enabled=True
    )

    # 注册任务
    scheduler.register_task(task_id, gmail_sync_task, config)

    logger.info(f"Gmail同步定时任务已配置: 项目={project_code}, Cron={cron_expr}")

    return scheduler, api


def start_gmail_sync_scheduler(project_code: str = "OOG120", cron_expr: str = "0 20 * * *"):
    """启动Gmail同步定时任务调度器

    Args:
        project_code: 项目编码
        cron_expr: Cron表达式
    """
    scheduler, api = setup_gmail_sync_scheduler(project_code, cron_expr)

    try:
        # 启动调度器
        scheduler.start()
        logger.info(f"Gmail同步定时任务调度器已启动，项目: {project_code}, Cron: {cron_expr}")

        # 返回调度器和API供外部使用
        return scheduler, api

    except Exception as e:
        logger.error(f"启动Gmail同步定时任务调度器失败: {e}")
        raise


def stop_gmail_sync_scheduler(scheduler: TaskScheduler):
    """停止Gmail同步定时任务调度器"""
    try:
        scheduler.shutdown()
        logger.info("Gmail同步定时任务调度器已停止")
    except Exception as e:
        logger.error(f"停止Gmail同步定时任务调度器失败: {e}")


if __name__ == "__main__":
    """测试运行"""
    import time
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    logger.info("=== Gmail同步定时任务测试 ===")
    
    # 启动调度器
    scheduler, api = start_gmail_sync_scheduler("OOG120", "0 20 * * *")

    try:
        # 获取任务信息
        task_info = api.get_task_info("gmail_sync_oog120")
        logger.info(f"任务信息: {task_info}")
        
        # 运行一段时间进行测试
        logger.info("调度器将运行120秒进行测试...")
        time.sleep(120)
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    
    finally:
        # 停止调度器
        stop_gmail_sync_scheduler(scheduler)
