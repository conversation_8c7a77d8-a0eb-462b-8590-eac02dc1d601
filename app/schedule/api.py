"""
定时任务前端交互接口
提供简单的函数接口供前端调用
"""
import logging
from typing import Dict, Any, Optional, List
from .scheduler import TaskScheduler
from .models import TaskConfig


class ScheduleAPI:
    """定时任务API接口"""
    
    def __init__(self, scheduler: TaskScheduler):
        """
        初始化API接口
        
        Args:
            scheduler: 任务调度器实例
        """
        self.scheduler = scheduler
        self.logger = logging.getLogger(__name__)
    
    def update_task_schedule(self, task_id: str, cron_expr: str) -> Dict[str, Any]:
        """
        更新任务调度配置
        
        Args:
            task_id: 任务ID
            cron_expr: cron表达式，如 "0 21 * * *" 表示每晚9点
            
        Returns:
            操作结果
            {
                "success": bool,
                "message": str,
                "data": dict or None
            }
        """
        try:
            if not task_id:
                return {
                    "success": False,
                    "message": "任务ID不能为空",
                    "data": None
                }
            
            if not cron_expr:
                return {
                    "success": False,
                    "message": "cron表达式不能为空",
                    "data": None
                }
            
            success = self.scheduler.set_task_cron(task_id, cron_expr)
            
            if success:
                task_status = self.scheduler.get_task_status(task_id)
                self.logger.info("Task schedule updated via API: %s -> %s", task_id, cron_expr)
                return {
                    "success": True,
                    "message": f"任务 {task_id} 调度配置更新成功",
                    "data": task_status
                }
            else:
                return {
                    "success": False,
                    "message": f"任务 {task_id} 调度配置更新失败，请检查任务是否存在或cron表达式是否正确",
                    "data": None
                }
                
        except Exception as e:
            self.logger.error("API error in update_task_schedule: %s", str(e))
            return {
                "success": False,
                "message": f"更新任务调度配置时发生错误: {str(e)}",
                "data": None
            }
    
    def remove_task(self, task_id: str) -> Dict[str, Any]:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作结果
            {
                "success": bool,
                "message": str,
                "data": dict or None
            }
        """
        try:
            if not task_id:
                return {
                    "success": False,
                    "message": "任务ID不能为空",
                    "data": None
                }
            
            # 获取移除前的任务状态
            task_status = self.scheduler.get_task_status(task_id)
            
            success = self.scheduler.remove_task(task_id)
            
            if success:
                self.logger.info("Task removed via API: %s", task_id)
                return {
                    "success": True,
                    "message": f"任务 {task_id} 移除成功",
                    "data": task_status
                }
            else:
                return {
                    "success": False,
                    "message": f"任务 {task_id} 移除失败，请检查任务是否存在",
                    "data": None
                }
                
        except Exception as e:
            self.logger.error("API error in remove_task: %s", str(e))
            return {
                "success": False,
                "message": f"移除任务时发生错误: {str(e)}",
                "data": None
            }
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态信息
        """
        try:
            if not task_id:
                return {
                    "success": False,
                    "message": "任务ID不能为空",
                    "data": None
                }

            status = self.scheduler.get_task_status(task_id)
            if status:
                return {
                    "success": True,
                    "message": "获取任务状态成功",
                    "data": status
                }
            else:
                return {
                    "success": False,
                    "message": f"任务 {task_id} 不存在",
                    "data": None
                }

        except Exception as e:
            self.logger.error("API error in get_task_status: %s", str(e))
            return {
                "success": False,
                "message": f"获取任务状态时发生错误: {str(e)}",
                "data": None
            }

    def get_task_info(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务信息
            {
                "success": bool,
                "message": str,
                "data": dict or None
            }
        """
        try:
            if not task_id:
                return {
                    "success": False,
                    "message": "任务ID不能为空",
                    "data": None
                }
            
            task_status = self.scheduler.get_task_status(task_id)
            
            if task_status:
                return {
                    "success": True,
                    "message": "获取任务信息成功",
                    "data": task_status
                }
            else:
                return {
                    "success": False,
                    "message": f"任务 {task_id} 不存在",
                    "data": None
                }
                
        except Exception as e:
            self.logger.error("API error in get_task_info: %s", str(e))
            return {
                "success": False,
                "message": f"获取任务信息时发生错误: {str(e)}",
                "data": None
            }
    
    def list_all_tasks(self) -> Dict[str, Any]:
        """
        获取所有任务列表
        
        Returns:
            所有任务信息
            {
                "success": bool,
                "message": str,
                "data": dict or None
            }
        """
        try:
            tasks = self.scheduler.list_tasks()
            
            return {
                "success": True,
                "message": f"获取任务列表成功，共 {len(tasks)} 个任务",
                "data": {
                    "tasks": tasks,
                    "total_count": len(tasks)
                }
            }
            
        except Exception as e:
            self.logger.error("API error in list_all_tasks: %s", str(e))
            return {
                "success": False,
                "message": f"获取任务列表时发生错误: {str(e)}",
                "data": None
            }
    
    def enable_task(self, task_id: str) -> Dict[str, Any]:
        """
        启用任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作结果
        """
        try:
            if task_id not in self.scheduler.task_configs:
                return {
                    "success": False,
                    "message": f"任务 {task_id} 不存在",
                    "data": None
                }
            
            config = self.scheduler.task_configs[task_id]
            if config.enabled:
                return {
                    "success": True,
                    "message": f"任务 {task_id} 已经是启用状态",
                    "data": self.scheduler.get_task_status(task_id)
                }
            
            config.enabled = True
            self.scheduler._add_job(task_id, config)
            
            self.logger.info("Task enabled via API: %s", task_id)
            return {
                "success": True,
                "message": f"任务 {task_id} 启用成功",
                "data": self.scheduler.get_task_status(task_id)
            }
            
        except Exception as e:
            self.logger.error("API error in enable_task: %s", str(e))
            return {
                "success": False,
                "message": f"启用任务时发生错误: {str(e)}",
                "data": None
            }
    
    def disable_task(self, task_id: str) -> Dict[str, Any]:
        """
        禁用任务（不删除，只是停止调度）
        
        Args:
            task_id: 任务ID
            
        Returns:
            操作结果
        """
        try:
            if task_id not in self.scheduler.task_configs:
                return {
                    "success": False,
                    "message": f"任务 {task_id} 不存在",
                    "data": None
                }
            
            config = self.scheduler.task_configs[task_id]
            if not config.enabled:
                return {
                    "success": True,
                    "message": f"任务 {task_id} 已经是禁用状态",
                    "data": self.scheduler.get_task_status(task_id)
                }
            
            config.enabled = False
            
            # 从调度器中移除，但保留配置
            if self.scheduler.scheduler.get_job(task_id):
                self.scheduler.scheduler.remove_job(task_id)
            
            self.logger.info("Task disabled via API: %s", task_id)
            return {
                "success": True,
                "message": f"任务 {task_id} 禁用成功",
                "data": self.scheduler.get_task_status(task_id)
            }
            
        except Exception as e:
            self.logger.error("API error in disable_task: %s", str(e))
            return {
                "success": False,
                "message": f"禁用任务时发生错误: {str(e)}",
                "data": None
            }


# 全局API实例（后续可以通过依赖注入优化）
_schedule_api: Optional[ScheduleAPI] = None


def get_schedule_api() -> ScheduleAPI:
    """获取调度API实例"""
    global _schedule_api
    if _schedule_api is None:
        raise RuntimeError("Schedule API not initialized. Call init_schedule_api() first.")
    return _schedule_api


def init_schedule_api(scheduler: TaskScheduler) -> ScheduleAPI:
    """初始化调度API"""
    global _schedule_api
    _schedule_api = ScheduleAPI(scheduler)
    return _schedule_api


# 便捷函数，供前端直接调用
def update_task_schedule(task_id: str, cron_expr: str) -> Dict[str, Any]:
    """更新任务调度配置的便捷函数"""
    return get_schedule_api().update_task_schedule(task_id, cron_expr)


def remove_task(task_id: str) -> Dict[str, Any]:
    """移除任务的便捷函数"""
    return get_schedule_api().remove_task(task_id)


def get_task_info(task_id: str) -> Dict[str, Any]:
    """获取任务信息的便捷函数"""
    return get_schedule_api().get_task_info(task_id)


def list_all_tasks() -> Dict[str, Any]:
    """获取所有任务列表的便捷函数"""
    return get_schedule_api().list_all_tasks()
