"""
文件处理工具
"""
import os
import uuid
from pathlib import Path
from typing import Optional
from fastapi import UploadFile, HTTPException

from app.config import settings


class FileHandler:
    """文件处理类"""
    
    @staticmethod
    def validate_file(file: UploadFile) -> None:
        """验证上传的文件"""
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件扩展名
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in settings.ALLOWED_FILE_EXTENSIONS:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件格式。支持的格式: {', '.join(settings.ALLOWED_FILE_EXTENSIONS)}"
            )
        
        # 检查文件大小（如果可以获取）
        if hasattr(file, 'size') and file.size and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400, 
                detail=f"文件大小超过限制。最大允许: {settings.MAX_FILE_SIZE / 1024 / 1024:.1f}MB"
            )
    
    @staticmethod
    def generate_unique_filename(original_filename: str) -> str:
        """生成唯一的文件名"""
        file_ext = Path(original_filename).suffix.lower()
        unique_id = str(uuid.uuid4())
        return f"{unique_id}{file_ext}"
    
    @staticmethod
    async def save_payment_screenshot(file: UploadFile) -> str:
        """保存支付截图文件"""
        # 验证文件
        FileHandler.validate_file(file)
        
        # 生成唯一文件名
        filename = FileHandler.generate_unique_filename(file.filename)
        
        # 构建完整路径
        file_path = os.path.join(settings.payment_screenshot_path, filename)
        
        try:
            # 保存文件
            content = await file.read()
            
            # 再次检查文件大小
            if len(content) > settings.MAX_FILE_SIZE:
                raise HTTPException(
                    status_code=400, 
                    detail=f"文件大小超过限制。最大允许: {settings.MAX_FILE_SIZE / 1024 / 1024:.1f}MB"
                )
            
            with open(file_path, "wb") as f:
                f.write(content)
            
            return filename
            
        except Exception as e:
            # 如果保存失败，删除可能已创建的文件
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=500, detail=f"文件保存失败: {str(e)}")
    
    @staticmethod
    def get_payment_screenshot_path(filename: str) -> Optional[str]:
        """获取支付截图文件的完整路径"""
        if not filename:
            return None
        
        file_path = os.path.join(settings.payment_screenshot_path, filename)
        if os.path.exists(file_path):
            return file_path
        return None
    
    @staticmethod
    def delete_payment_screenshot(filename: str) -> bool:
        """删除支付截图文件"""
        if not filename:
            return True
        
        file_path = os.path.join(settings.payment_screenshot_path, filename)
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_file_content_type(filename: str) -> str:
        """根据文件扩展名获取Content-Type"""
        file_ext = Path(filename).suffix.lower()
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.pdf': 'application/pdf'
        }
        return content_types.get(file_ext, 'application/octet-stream')
