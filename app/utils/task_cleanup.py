"""
任务状态清理工具
"""
from datetime import datetime, timedelta
from typing import Dict, Any
from app.logging_config import get_task_logger

logger = get_task_logger("task_cleanup")


def cleanup_old_tasks(task_store: Dict[str, Any], max_age_hours: int = 24) -> int:
    """
    清理过期的任务状态
    
    Args:
        task_store: 任务状态存储字典
        max_age_hours: 最大保留时间（小时）
    
    Returns:
        清理的任务数量
    """
    if not task_store:
        return 0
    
    cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
    tasks_to_remove = []
    
    for task_id, task_info in task_store.items():
        try:
            # 检查任务的更新时间
            updated_at = task_info.get('updated_at')
            if updated_at and updated_at < cutoff_time:
                # 只清理已完成或失败的任务
                status = task_info.get('status')
                if status in ['completed', 'failed']:
                    tasks_to_remove.append(task_id)
        except Exception as e:
            logger.error(f"检查任务 {task_id} 时出错: {str(e)}")
            # 如果任务信息损坏，也将其标记为删除
            tasks_to_remove.append(task_id)
    
    # 删除过期任务
    for task_id in tasks_to_remove:
        try:
            del task_store[task_id]
            logger.debug(f"清理过期任务: {task_id}")
        except KeyError:
            pass  # 任务可能已被其他地方删除
    
    if tasks_to_remove:
        logger.info(f"清理了 {len(tasks_to_remove)} 个过期任务")
    
    return len(tasks_to_remove)


def get_task_store_stats(task_store: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取任务存储的统计信息
    
    Args:
        task_store: 任务状态存储字典
    
    Returns:
        统计信息字典
    """
    if not task_store:
        return {
            "total_tasks": 0,
            "pending_tasks": 0,
            "running_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "oldest_task_age_hours": 0
        }
    
    stats = {
        "total_tasks": len(task_store),
        "pending_tasks": 0,
        "running_tasks": 0,
        "completed_tasks": 0,
        "failed_tasks": 0,
        "oldest_task_age_hours": 0
    }
    
    now = datetime.now()
    oldest_time = now
    
    for task_info in task_store.values():
        try:
            status = task_info.get('status', 'unknown')
            if status == 'pending':
                stats["pending_tasks"] += 1
            elif status == 'running':
                stats["running_tasks"] += 1
            elif status == 'completed':
                stats["completed_tasks"] += 1
            elif status == 'failed':
                stats["failed_tasks"] += 1
            
            # 找到最老的任务
            created_at = task_info.get('created_at')
            if created_at and created_at < oldest_time:
                oldest_time = created_at
        except Exception as e:
            logger.error(f"统计任务信息时出错: {str(e)}")
    
    # 计算最老任务的年龄
    if oldest_time < now:
        age_delta = now - oldest_time
        stats["oldest_task_age_hours"] = round(age_delta.total_seconds() / 3600, 2)
    
    return stats


def force_cleanup_task(task_store: Dict[str, Any], task_id: str) -> bool:
    """
    强制清理指定任务
    
    Args:
        task_store: 任务状态存储字典
        task_id: 要清理的任务ID
    
    Returns:
        是否成功清理
    """
    try:
        if task_id in task_store:
            del task_store[task_id]
            logger.info(f"强制清理任务: {task_id}")
            return True
        else:
            logger.warning(f"任务不存在，无法清理: {task_id}")
            return False
    except Exception as e:
        logger.error(f"强制清理任务 {task_id} 失败: {str(e)}")
        return False


def cleanup_all_completed_tasks(task_store: Dict[str, Any]) -> int:
    """
    清理所有已完成的任务（不考虑时间）
    
    Args:
        task_store: 任务状态存储字典
    
    Returns:
        清理的任务数量
    """
    if not task_store:
        return 0
    
    tasks_to_remove = []
    
    for task_id, task_info in task_store.items():
        try:
            status = task_info.get('status')
            if status in ['completed', 'failed']:
                tasks_to_remove.append(task_id)
        except Exception as e:
            logger.error(f"检查任务 {task_id} 状态时出错: {str(e)}")
    
    # 删除已完成的任务
    for task_id in tasks_to_remove:
        try:
            del task_store[task_id]
        except KeyError:
            pass
    
    if tasks_to_remove:
        logger.info(f"清理了 {len(tasks_to_remove)} 个已完成的任务")
    
    return len(tasks_to_remove)
