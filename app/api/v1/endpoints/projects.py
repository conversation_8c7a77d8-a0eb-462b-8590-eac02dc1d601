"""
项目管理API端点
"""
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import project
from app.schemas.project import (
    Project,
    ProjectCreate,
    ProjectUpdate,
    ProjectListResponse,
)

router = APIRouter()


@router.get("/", response_model=ProjectListResponse)
def get_projects(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    name_pattern: Optional[str] = Query(None, description="项目名称模糊搜索"),
    db: Session = Depends(get_db)
):
    """获取项目列表"""
    if name_pattern:
        projects = project.get_multi_by_name(
            db, name_pattern=name_pattern, skip=skip, limit=limit
        )
        # 简单计算总数（实际应用中可能需要优化）
        total = len(project.get_multi_by_name(db, name_pattern=name_pattern, skip=0, limit=10000))
    else:
        projects = project.get_multi(db, skip=skip, limit=limit)
        total = len(project.get_multi(db, skip=0, limit=10000))
    
    return ProjectListResponse(
        items=projects,
        total=total,
        skip=skip,
        limit=limit
    )




@router.get("/{project_code}", response_model=Project)
def get_project(
    project_code: str,
    db: Session = Depends(get_db)
):
    """根据项目代码获取项目详情"""
    db_project = project.get_by_code(db, code=project_code)
    if not db_project:
        raise HTTPException(status_code=404, detail="项目不存在")
    return db_project


@router.post("/", response_model=Project)
def create_project(
    project_in: ProjectCreate,
    db: Session = Depends(get_db)
):
    """创建新项目"""
    try:
        return project.create_project(db, obj_in=project_in)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{project_code}", response_model=Project)
def update_project(
    project_code: str,
    project_in: ProjectUpdate,
    db: Session = Depends(get_db)
):
    """更新项目信息"""
    db_project = project.update_project(db, code=project_code, obj_in=project_in)
    if not db_project:
        raise HTTPException(status_code=404, detail="项目不存在")
    return db_project


@router.delete("/{project_code}", response_model=Project)
def delete_project(
    project_code: str,
    db: Session = Depends(get_db)
):
    """删除项目"""
    db_project = project.delete_project(db, code=project_code)
    if not db_project:
        raise HTTPException(status_code=404, detail="项目不存在")
    return db_project
