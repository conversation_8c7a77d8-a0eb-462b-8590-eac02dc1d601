"""
KOL管理API端点
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import kol
from app.models.enums import PlatformEnum, KOLTierEnum, EmailFetchStatusEnum
from app.models.kol import KOL as KOLModel
from app.schemas.kol import (
    KOL,
    KOLCreate,
    KOLUpdate,
    KOLSearch,
    KOLListResponse,
    KOLStatsResponse
)

router = APIRouter()


@router.get("/", response_model=KOLListResponse)
def get_kols(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    kol_id: Optional[int] = Query(None, description="KOL主键ID", ge=1),
    project_code: Optional[str] = Query(None, description="项目编码"),
    platform: Optional[PlatformEnum] = Query(None, description="平台"),
    tier: Optional[KOLTierEnum] = Query(None, description="KOL分级"),
    has_email: Optional[bool] = Query(None, description="是否有邮箱"),
    email_fetch_status: Optional[str] = Query(None, description="邮箱获取状态"),
    ai_matched: Optional[bool] = Query(None, description="AI是否匹配"),
    created_after: Optional[datetime] = Query(None, description="创建时间起始（包含）"),
    created_before: Optional[datetime] = Query(None, description="创建时间结束（包含）"),
    db: Session = Depends(get_db)
):
    """获取KOL列表 - 支持基础搜索参数"""
    # 使用统一的搜索函数处理所有参数
    kols = kol.search_kols(
        db,
        kol_id=kol_id,
        project_code=project_code,
        platform=platform,
        tier=tier,
        has_email=has_email,
        email_fetch_status=email_fetch_status,
        ai_matched=ai_matched,
        created_after=created_after,
        created_before=created_before,
        skip=skip,
        limit=limit
    )

    # 直接使用len(kols)计算总数可能会有问题:
    # 1. 当数据量很大时,返回所有数据会占用大量内存
    # 2. 分页查询时,len(kols)只是当前页的数量,不是总数
    # 所以需要单独查询总数
    total = len(kol.search_kols(
        db,
        kol_id=kol_id,
        project_code=project_code,
        platform=platform,
        tier=tier,
        has_email=has_email,
        email_fetch_status=email_fetch_status,
        ai_matched=ai_matched,
        created_after=created_after,
        created_before=created_before,
        skip=0,
        limit=10000  # 设置较大的limit以获取所有匹配记录
    ))
    
    return KOLListResponse(
        items=kols,
        total=total,
        skip=skip,
        limit=limit
    )


@router.post("/search", response_model=KOLListResponse)
def search_kols(
    search_params: KOLSearch,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db)
):
    """高级搜索KOL"""
    kols = kol.search_kols(
        db,
        kol_id=search_params.kol_id,
        nick_name=search_params.nick_name,
        social_id=search_params.social_id,
        platform=search_params.platform,
        tier=search_params.tier,
        min_followers=search_params.min_followers,
        max_followers=search_params.max_followers,
        min_engagement_rate=search_params.min_engagement_rate,
        max_engagement_rate=search_params.max_engagement_rate,
        min_mean_views_k=search_params.min_mean_views_k,
        max_mean_views_k=search_params.max_mean_views_k,
        min_median_views_k=search_params.min_median_views_k,
        max_median_views_k=search_params.max_median_views_k,
        min_ai_score=search_params.min_ai_score,
        max_ai_score=search_params.max_ai_score,
        created_after=search_params.created_after,
        created_before=search_params.created_before,
        updated_after=search_params.updated_after,
        updated_before=search_params.updated_before,
        ai_matched=search_params.ai_matched,
        source=search_params.source,
        email=search_params.email,
        email_fetch_status=search_params.email_fetch_status,
        hashtags=search_params.hashtags,
        topics=search_params.topics,
        captions=search_params.captions,
        hashtags_fuzzy=search_params.hashtags_fuzzy,
        topics_fuzzy=search_params.topics_fuzzy,
        captions_fuzzy=search_params.captions_fuzzy,
        project_code=search_params.project_code,
        crawler_task_id=search_params.crawler_task_id,
        has_email=search_params.has_email,
        has_bio_extracted_email=search_params.has_bio_extracted_email,
        has_nano_extracted_email=search_params.has_nano_extracted_email,
        is_nano_unlocked=search_params.is_nano_unlocked,
        is_ai_mismatched=search_params.is_ai_mismatched,
        skip=skip,
        limit=limit
    )
    
    # 计算总数
    total = len(kol.search_kols(
        db,
        kol_id=search_params.kol_id,
        nick_name=search_params.nick_name,
        social_id=search_params.social_id,
        platform=search_params.platform,
        tier=search_params.tier,
        min_followers=search_params.min_followers,
        max_followers=search_params.max_followers,
        min_engagement_rate=search_params.min_engagement_rate,
        max_engagement_rate=search_params.max_engagement_rate,
        min_mean_views_k=search_params.min_mean_views_k,
        max_mean_views_k=search_params.max_mean_views_k,
        min_median_views_k=search_params.min_median_views_k,
        max_median_views_k=search_params.max_median_views_k,
        min_ai_score=search_params.min_ai_score,
        max_ai_score=search_params.max_ai_score,
        created_after=search_params.created_after,
        created_before=search_params.created_before,
        updated_after=search_params.updated_after,
        updated_before=search_params.updated_before,
        ai_matched=search_params.ai_matched,
        source=search_params.source,
        email=search_params.email,
        email_fetch_status=search_params.email_fetch_status,
        hashtags=search_params.hashtags,
        topics=search_params.topics,
        captions=search_params.captions,
        hashtags_fuzzy=search_params.hashtags_fuzzy,
        topics_fuzzy=search_params.topics_fuzzy,
        captions_fuzzy=search_params.captions_fuzzy,
        project_code=search_params.project_code,
        crawler_task_id=search_params.crawler_task_id,
        has_email=search_params.has_email,
        has_bio_extracted_email=search_params.has_bio_extracted_email,
        has_nano_extracted_email=search_params.has_nano_extracted_email,
        is_nano_unlocked=search_params.is_nano_unlocked,
        is_ai_mismatched=search_params.is_ai_mismatched,
        skip=0,
        limit=10000
    ))
    
    return KOLListResponse(
        items=kols,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/stats", response_model=KOLStatsResponse)
def get_kol_stats(
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db)
):
    """获取KOL统计信息"""
    stats = kol.get_kol_statistics(db, project_code=project_code)
    return KOLStatsResponse(**stats)


@router.get("/{social_id}/{platform}/{project_code}", response_model=KOL)
def get_kol(
    social_id: str,
    platform: PlatformEnum,
    project_code: str,
    db: Session = Depends(get_db)
):
    """根据社交媒体ID、平台和项目代码获取KOL详情"""
    db_kol = kol.get_by_social_id_platform_project(db, social_id=social_id, platform=platform, project_code=project_code)
    if not db_kol:
        raise HTTPException(status_code=404, detail="KOL不存在")
    return db_kol


@router.post("/", response_model=KOL)
def create_kol(
    kol_in: KOLCreate,
    db: Session = Depends(get_db)
):
    """创建新KOL"""
    try:
        return kol.create_kol(db, obj_in=kol_in)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{social_id}/{platform}/{project_code}", response_model=KOL)
def update_kol(
    social_id: str,
    platform: PlatformEnum,
    project_code: str,
    kol_in: KOLUpdate,
    db: Session = Depends(get_db)
):
    """更新KOL信息"""
    db_kol = kol.get_by_social_id_platform_project(db, social_id=social_id, platform=platform, project_code=project_code)
    if not db_kol:
        raise HTTPException(status_code=404, detail="KOL不存在")
    return kol.update(db, db_obj=db_kol, obj_in=kol_in)


@router.delete("/{social_id}/{platform}/{project_code}", response_model=KOL)
def delete_kol(
    social_id: str,
    platform: PlatformEnum,
    project_code: str,
    db: Session = Depends(get_db)
):
    """删除KOL"""
    db_kol = kol.get_by_social_id_platform_project(db, social_id=social_id, platform=platform, project_code=project_code)
    if not db_kol:
        raise HTTPException(status_code=404, detail="KOL不存在")
    return kol.remove(db, id=db_kol.id)


# 邮箱获取相关API接口

@router.get("/need-email-fetch", response_model=KOLListResponse)
def get_kols_need_email_fetch(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db)
):
    """获取需要进行邮箱获取的KOL列表"""
    kols = kol.get_kols_need_email_fetch(db, project_code=project_code, limit=limit)
    total = len(kols)

    return KOLListResponse(
        items=kols[skip:skip+limit],
        total=total,
        skip=skip,
        limit=limit
    )


@router.post("/{kol_id}/fetch-email")
def fetch_kol_email(
    kol_id: int,
    db: Session = Depends(get_db)
):
    """为指定KOL执行邮箱获取流程"""
    from app.tasks.steps.fetch_kol_email_step import FetchKOLEmailStep

    # 检查KOL是否存在
    db_kol = kol.get_by_kol_id(db, kol_id=kol_id)
    if not db_kol:
        raise HTTPException(status_code=404, detail="KOL不存在")

    try:
        # 执行邮箱获取步骤
        step = FetchKOLEmailStep({"kol_id": kol_id})
        result = step.run()

        return {
            "message": "邮箱获取流程执行完成",
            "kol_id": kol_id,
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"邮箱获取失败: {str(e)}")


@router.put("/{kol_id}/email-fetch-status")
def update_kol_email_fetch_status(
    kol_id: int,
    status: EmailFetchStatusEnum = Query(..., description="邮箱获取状态"),
    db: Session = Depends(get_db)
):
    """更新KOL的邮箱获取状态"""
    # 检查KOL是否存在
    db_kol = kol.get_by_kol_id(db, kol_id=kol_id)
    if not db_kol:
        raise HTTPException(status_code=404, detail="KOL不存在")

    # 更新状态
    updated_kol = kol.update(db, db_obj=db_kol, obj_in={"email_fetch_status": status})

    return {
        "message": "邮箱获取状态更新成功",
        "kol_id": kol_id,
        "status": status,
        "updated_kol": updated_kol
    }


# AI匹配相关API接口

@router.get("/matched", response_model=KOLListResponse)
def get_matched_kols(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db)
):
    """获取AI匹配的KOL列表"""
    kols = kol.get_matched_kols(db, project_code=project_code, skip=skip, limit=limit)
    total = len(kol.get_by_ai_matched_status(db, ai_matched=True, project_code=project_code, skip=0, limit=10000))

    return KOLListResponse(
        items=kols,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/unmatched", response_model=KOLListResponse)
def get_unmatched_kols(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db)
):
    """获取AI未匹配的KOL列表"""
    kols = kol.get_unmatched_kols(db, project_code=project_code, skip=skip, limit=limit)
    total = len(kol.get_by_ai_matched_status(db, ai_matched=False, project_code=project_code, skip=0, limit=10000))

    return KOLListResponse(
        items=kols,
        total=total,
        skip=skip,
        limit=limit
    )


@router.put("/{kol_id}/ai-matched")
def update_kol_ai_matched_status(
    kol_id: int,
    ai_matched: bool = Query(..., description="AI匹配状态"),
    db: Session = Depends(get_db)
):
    """更新KOL的AI匹配状态"""
    # 检查KOL是否存在
    db_kol = kol.get_by_kol_id(db, kol_id=kol_id)
    if not db_kol:
        raise HTTPException(status_code=404, detail="KOL不存在")

    # 更新AI匹配状态
    updated_kol = kol.update(db, db_obj=db_kol, obj_in={"ai_matched": ai_matched})

    return {
        "message": "AI匹配状态更新成功",
        "kol_id": kol_id,
        "ai_matched": ai_matched,
        "updated_kol": updated_kol
    }


# 邮箱获取进度查询接口

@router.get("/email-fetch-progress")
def get_email_fetch_progress(
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db)
):
    """获取邮箱获取流程的整体进度统计"""
    try:
        # 构建基础查询
        base_query = db.query(KOLModel)
        if project_code:
            base_query = base_query.filter(KOLModel.project_code == project_code)

        # 统计各个状态的数量
        total_kols = base_query.count()

        # 按邮箱获取状态统计
        status_stats = {}
        for status in EmailFetchStatusEnum:
            count = base_query.filter(KOLModel.email_fetch_status == status).count()
            status_stats[status.value.lower()] = count

        # 按AI匹配状态统计
        ai_matched_count = base_query.filter(KOLModel.ai_matched == True).count()
        ai_unmatched_count = base_query.filter(KOLModel.ai_matched == False).count()
        ai_pending_count = base_query.filter(KOLModel.ai_matched.is_(None)).count()

        # 邮箱获取统计
        has_email_count = base_query.filter(KOLModel.email.isnot(None)).count()
        no_email_count = total_kols - has_email_count

        # 计算进度百分比
        progress_percentage = 0
        if total_kols > 0:
            completed_count = status_stats.get('completed', 0)
            progress_percentage = round((completed_count / total_kols) * 100, 2)

        return {
            "project_code": project_code,
            "total_kols": total_kols,
            "progress_percentage": progress_percentage,
            "email_fetch_status": status_stats,
            "ai_match_status": {
                "matched": ai_matched_count,
                "unmatched": ai_unmatched_count,
                "pending": ai_pending_count
            },
            "email_status": {
                "has_email": has_email_count,
                "no_email": no_email_count
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取进度统计失败: {str(e)}")


@router.get("/email-fetch-summary")
def get_email_fetch_summary(
    project_code: Optional[str] = Query(None, description="项目编码"),
    db: Session = Depends(get_db)
):
    """获取邮箱获取流程的详细摘要"""
    try:
        # 构建基础查询
        base_query = db.query(KOLModel)
        if project_code:
            base_query = base_query.filter(KOLModel.project_code == project_code)

        # 需要进行bio解析的KOL（状态为PENDING且无邮箱）
        need_bio_parse = base_query.filter(
            KOLModel.email_fetch_status == 'PENDING',
            KOLModel.email.is_(None)
        ).count()

        # 需要AI评分的KOL（bio已解析但未AI评分）
        need_ai_score = base_query.filter(
            KOLModel.email_fetch_status == 'BIO_PARSED'
        ).count()

        # 需要nano获取的KOL（AI已评分且匹配，但未调用nano）
        need_nano_fetch = base_query.filter(
            KOLModel.email_fetch_status == 'AI_SCORED',
            KOLModel.ai_matched == True
        ).count()

        # 已完成的KOL
        completed = base_query.filter(
            KOLModel.email_fetch_status == 'COMPLETED'
        ).count()

        # AI未匹配的KOL（不需要继续处理）
        ai_unmatched = base_query.filter(
            KOLModel.ai_matched == False
        ).count()

        return {
            "project_code": project_code,
            "summary": {
                "need_bio_parse": need_bio_parse,
                "need_ai_score": need_ai_score,
                "need_nano_fetch": need_nano_fetch,
                "completed": completed,
                "ai_unmatched": ai_unmatched
            },
            "next_actions": {
                "bio_parse_ready": need_bio_parse > 0,
                "ai_score_ready": need_ai_score > 0,
                "nano_fetch_ready": need_nano_fetch > 0
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取摘要失败: {str(e)}")
