"""
绩效和支付统一管理API端点
"""
from typing import Optional
from datetime import datetime
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, Form, File, UploadFile
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import os

from app.api.deps import get_db
from app.crud.performance_payment import performance_payment
from app.schemas.performance_payment import PerformancePaymentCreate, PerformancePaymentResponse
from app.models.enums import PlatformEnum
from app.utils.file_handler import FileHandler

router = APIRouter()


@router.post("/", response_model=PerformancePaymentResponse)
async def create_performance_payment(
    platform: PlatformEnum = Form(..., description="KOL所在平台"),
    social_id: str = Form(..., description="KOL的社交媒体ID"),
    project_code: str = Form(..., description="项目编码"),
    post_link: str = Form(..., description="帖子链接"),
    post_date: Optional[datetime] = Form(None, description="发布日期"),
    payment_amount: Optional[Decimal] = Form(None, description="支付金额"),
    paypal_accounts: Optional[str] = Form(None, description="PayPal账户"),
    tracker: Optional[str] = Form(None, description="跟进人"),
    payout_date: Optional[datetime] = Form(None, description="支付日期"),
    fund_source: Optional[str] = Form(None, description="资金来源"),
    note: Optional[str] = Form(None, description="备注"),
    payment_screenshot_file: Optional[UploadFile] = File(None, description="支付截图文件"),
    db: Session = Depends(get_db)
):
    """
    统一创建绩效和支付记录
    
    使用数据库事务保证数据一致性，支持可选的支付截图文件上传。
    通过 platform + social_id + project_code 自动查找对应的KOL。
    """
    # 处理文件上传
    screenshot_filename = None
    if payment_screenshot_file:
        try:
            screenshot_filename = await FileHandler.save_payment_screenshot(payment_screenshot_file)
        except HTTPException as e:
            raise e
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")
    
    # 创建统一数据对象
    create_data = PerformancePaymentCreate(
        platform=platform,
        social_id=social_id,
        project_code=project_code,
        post_link=post_link,
        post_date=post_date,
        payment_amount=payment_amount,
        paypal_accounts=paypal_accounts,
        tracker=tracker,
        payout_date=payout_date,
        fund_source=fund_source,
        note=note
    )
    
    try:
        # 使用事务创建绩效和支付记录
        performance_obj, payment_obj = performance_payment.create_performance_payment(
            db, 
            obj_in=create_data,
            payment_screenshot_filename=screenshot_filename
        )
        
        # 转换为响应格式
        return performance_payment.convert_to_response(performance_obj, payment_obj)
        
    except ValueError as e:
        # 如果创建失败，删除已上传的文件
        if screenshot_filename:
            FileHandler.delete_payment_screenshot(screenshot_filename)
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        # 如果创建失败，删除已上传的文件
        if screenshot_filename:
            FileHandler.delete_payment_screenshot(screenshot_filename)
        raise HTTPException(status_code=500, detail=f"创建记录失败: {str(e)}")


@router.get("/download-screenshot/{filename}")
async def download_payment_screenshot(
    filename: str
):
    """
    下载支付截图文件

    Args:
        filename: 支付截图文件名

    Returns:
        FileResponse: 文件下载响应
    """
    # 获取文件完整路径
    file_path = FileHandler.get_payment_screenshot_path(filename)

    if not file_path or not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")

    # 获取文件的Content-Type
    content_type = FileHandler.get_file_content_type(filename)

    # 返回文件下载响应
    return FileResponse(
        path=file_path,
        media_type=content_type,
        filename=filename,
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )
