"""
KOL匹配评估API端点
"""
from typing import List, Optional
from decimal import Decimal
import asyncio
import uuid
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.api.deps import get_db
from app.crud import kol
from app.services.llm_service import evaluate_kol_alignment, async_evaluate_kol_alignment
from app.logging_config import get_task_logger
from app.utils.concurrency_control import async_concurrency_limit
from app.utils.task_cleanup import cleanup_old_tasks, get_task_store_stats, force_cleanup_task

router = APIRouter()
logger = get_task_logger("kol_match")

# 内存中存储任务状态（生产环境建议使用Redis）
match_task_status_store = {}


class KOLMatchRequest(BaseModel):
    """KOL匹配评估请求schema"""
    kol_id: int = Field(..., description="KOL ID")
    prompt: str = Field(..., description="评估提示词")


class KOLMatchResponse(BaseModel):
    """KOL匹配评估响应schema"""
    kol_id: int = Field(..., description="KOL ID")
    score: Optional[Decimal] = Field(None, description="匹配分数")
    ai_matched: Optional[bool] = Field(None, description="是否匹配")
    success: bool = Field(..., description="评估是否成功")
    message: Optional[str] = Field(None, description="结果消息")


class BatchKOLMatchRequest(BaseModel):
    """批量KOL匹配评估请求schema"""
    kol_ids: List[int] = Field(..., description="KOL ID列表")
    prompt: str = Field(..., description="评估提示词")


class BatchKOLMatchResponse(BaseModel):
    """批量KOL匹配评估响应schema"""
    total_count: int = Field(..., description="总数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    results: List[KOLMatchResponse] = Field(..., description="详细结果列表")


class MatchTaskStartResponse(BaseModel):
    """匹配任务启动响应schema"""
    task_id: str = Field(..., description="任务ID")
    message: str = Field(..., description="启动消息")


class MatchTaskStatusResponse(BaseModel):
    """匹配任务状态响应schema"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态: pending/running/completed/failed")
    progress: int = Field(..., description="进度百分比 0-100")
    total_count: int = Field(..., description="总数量")
    processed_count: int = Field(..., description="已处理数量")
    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    message: Optional[str] = Field(None, description="状态消息")
    results: Optional[List[KOLMatchResponse]] = Field(None, description="完成时的结果列表")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


@router.post("/evaluate", response_model=KOLMatchResponse)
def evaluate_single_kol(
    request: KOLMatchRequest,
    db: Session = Depends(get_db)
):
    """评估单个KOL的匹配度"""
    try:
        # 检查KOL是否存在
        db_kol = kol.get_by_kol_id(db, kol_id=request.kol_id)
        if not db_kol:
            raise HTTPException(status_code=404, detail="KOL不存在")
        
        # 进行AI评分
        score = evaluate_kol_alignment(db, request.kol_id, request.prompt)
        
        if score is None:
            return KOLMatchResponse(
                kol_id=request.kol_id,
                score=None,
                ai_matched=False,
                success=False,
                message="AI评分失败"
            )
        
        # 更新KOL的AI评分状态
        kol.update_ai_score_status(db, kol_id=request.kol_id, ai_score=score)
        
        # 重新获取KOL以获取最新的ai_matched状态
        updated_kol = kol.get_by_kol_id(db, kol_id=request.kol_id)
        
        return KOLMatchResponse(
            kol_id=request.kol_id,
            score=score,
            ai_matched=updated_kol.ai_matched if updated_kol else False,
            success=True,
            message="评估成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"KOL {request.kol_id} 评估失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"评估失败: {str(e)}")


async def _evaluate_single_kol_async(kol_id: int, prompt: str) -> KOLMatchResponse:
    """异步评估单个KOL的匹配度"""
    # 创建新的数据库会话（线程安全）
    from app.db.session import SessionLocal
    db = SessionLocal()

    try:
        # 检查KOL是否存在
        db_kol = kol.get_by_kol_id(db, kol_id=kol_id)
        if not db_kol:
            return KOLMatchResponse(
                kol_id=kol_id,
                score=None,
                ai_matched=False,
                success=False,
                message="KOL不存在"
            )

        # 进行异步AI评分
        score, msg = await async_evaluate_kol_alignment(db, kol_id, prompt)

        if score is None:
            return KOLMatchResponse(
                kol_id=kol_id,
                score=None,
                ai_matched=False,
                success=False,
                message=msg
            )

        # 更新KOL的AI评分状态
        kol.update_ai_score_status(db, kol_id=kol_id, ai_score=score)

        # 重新获取KOL以获取最新的ai_matched状态
        updated_kol = kol.get_by_kol_id(db, kol_id=kol_id)

        return KOLMatchResponse(
            kol_id=kol_id,
            score=score,
            ai_matched=updated_kol.ai_matched if updated_kol else False,
            success=True,
            message="评估成功"
        )

    except Exception as e:
        logger.error(f"KOL {kol_id} 异步评估失败: {str(e)}")
        return KOLMatchResponse(
            kol_id=kol_id,
            score=None,
            ai_matched=False,
            success=False,
            message=f"评估失败: {str(e)}"
        )
    finally:
        db.close()


async def _background_batch_evaluate(task_id: str, kol_ids: List[int], prompt: str):
    """后台执行批量匹配评估任务"""
    try:
        # 更新任务状态为运行中
        match_task_status_store[task_id].update({
            "status": "running",
            "message": "正在评估中...",
            "updated_at": datetime.now()
        })

        logger.info(f"开始后台批量评估任务: {task_id}, KOL数量: {len(kol_ids)}")

        # 使用受控并发处理
        results = await _batch_evaluate_with_progress_update(task_id, kol_ids, prompt)

        # 统计结果
        success_count = sum(1 for r in results if isinstance(r, KOLMatchResponse) and r.success)
        failed_count = len(results) - success_count

        # 更新任务状态为完成
        match_task_status_store[task_id].update({
            "status": "completed",
            "progress": 100,
            "processed_count": len(kol_ids),
            "success_count": success_count,
            "failed_count": failed_count,
            "message": f"任务完成: 成功 {success_count}, 失败 {failed_count}",
            "results": results,
            "updated_at": datetime.now()
        })

        logger.info(f"后台批量评估任务完成: {task_id}, 成功: {success_count}, 失败: {failed_count}")

    except Exception as e:
        logger.error(f"后台批量评估任务失败: {task_id}, 错误: {str(e)}")
        match_task_status_store[task_id].update({
            "status": "failed",
            "message": f"任务失败: {str(e)}",
            "updated_at": datetime.now()
        })


async def _batch_evaluate_with_progress_update(task_id: str, kol_ids: List[int], prompt: str) -> List:
    """带进度更新的批量评估"""
    concurrency_semaphore = asyncio.Semaphore(5)
    processed_count = 0

    async def controlled_evaluate_with_progress(kol_id: int) -> KOLMatchResponse:
        nonlocal processed_count
        async with concurrency_semaphore:
            try:
                result = await _evaluate_single_kol_async(kol_id, prompt)
                processed_count += 1

                # 更新进度
                progress = int((processed_count / len(kol_ids)) * 100)
                match_task_status_store[task_id].update({
                    "progress": progress,
                    "processed_count": processed_count,
                    "updated_at": datetime.now()
                })

                return result
            except Exception as e:
                processed_count += 1
                logger.error(f"评估KOL {kol_id} 失败: {str(e)}")
                return KOLMatchResponse(
                    kol_id=kol_id,
                    score=None,
                    ai_matched=False,
                    success=False,
                    message=f"评估失败: {str(e)}"
                )

    # 创建任务
    tasks = [controlled_evaluate_with_progress(kol_id) for kol_id in kol_ids]

    # 执行并收集结果
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 处理异常结果
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            kol_id = kol_ids[i]
            error_response = KOLMatchResponse(
                kol_id=kol_id,
                score=None,
                ai_matched=False,
                success=False,
                message=f"处理异常: {str(result)}"
            )
            processed_results.append(error_response)
        else:
            processed_results.append(result)

    return processed_results


@router.post("/start-batch-evaluate", response_model=MatchTaskStartResponse)
async def start_batch_evaluate_kols(
    request: BatchKOLMatchRequest,
    background_tasks: BackgroundTasks
):
    """启动异步批量评估KOL匹配度任务"""
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 初始化任务状态
        now = datetime.now()
        match_task_status_store[task_id] = {
            "task_id": task_id,
            "status": "pending",
            "progress": 0,
            "total_count": len(request.kol_ids),
            "processed_count": 0,
            "success_count": 0,
            "failed_count": 0,
            "message": "任务已创建，等待开始",
            "results": None,
            "created_at": now,
            "updated_at": now
        }

        # 添加后台任务
        background_tasks.add_task(
            _background_batch_evaluate,
            task_id,
            request.kol_ids,
            request.prompt
        )

        logger.info(f"启动批量匹配评估任务: {task_id}, KOL数量: {len(request.kol_ids)}")

        return MatchTaskStartResponse(
            task_id=task_id,
            message=f"任务已启动，正在评估 {len(request.kol_ids)} 个KOL"
        )

    except Exception as e:
        logger.error(f"启动批量评估任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动任务失败: {str(e)}")


@router.get("/match-task-status/{task_id}", response_model=MatchTaskStatusResponse)
async def get_match_task_status(task_id: str):
    """获取匹配评估任务状态"""
    if task_id not in match_task_status_store:
        raise HTTPException(status_code=404, detail="任务不存在")

    task_info = match_task_status_store[task_id]
    return MatchTaskStatusResponse(**task_info)


@router.get("/status/{kol_id}")
def get_kol_match_status(
    kol_id: int,
    db: Session = Depends(get_db)
):
    """获取KOL的匹配状态"""
    db_kol = kol.get_by_kol_id(db, kol_id=kol_id)
    if not db_kol:
        raise HTTPException(status_code=404, detail="KOL不存在")

    return {
        "kol_id": kol_id,
        "ai_score": db_kol.ai_score,
        "ai_matched": db_kol.ai_matched,
        "ai_scored_at": db_kol.ai_scored_at,
        "email_fetch_status": db_kol.email_fetch_status
    }


@router.get("/match-task-stats")
async def get_match_task_stats():
    """获取匹配任务统计信息"""
    stats = get_task_store_stats(match_task_status_store)
    return {
        "match_tasks": stats,
        "total_tasks_in_memory": len(match_task_status_store)
    }


@router.post("/cleanup-match-tasks")
async def cleanup_match_tasks(max_age_hours: int = 24):
    """清理过期匹配任务"""
    cleaned_count = cleanup_old_tasks(match_task_status_store, max_age_hours)
    return {
        "message": f"清理了 {cleaned_count} 个过期匹配任务",
        "cleaned_count": cleaned_count,
        "remaining_tasks": len(match_task_status_store)
    }


@router.delete("/match-task/{task_id}")
async def delete_match_task(task_id: str):
    """删除指定匹配任务"""
    success = force_cleanup_task(match_task_status_store, task_id)
    if success:
        return {"message": f"匹配任务 {task_id} 已删除"}
    else:
        raise HTTPException(status_code=404, detail="任务不存在或删除失败")
