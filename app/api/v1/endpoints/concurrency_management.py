"""
并发控制管理API端点
"""
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from app.utils.concurrency_control import (
    get_current_concurrency_info,
    update_max_concurrency
)
from app.logging_config import get_task_logger

router = APIRouter()
logger = get_task_logger("concurrency_management")


class ConcurrencyInfoResponse(BaseModel):
    """并发控制信息响应schema"""
    max_concurrency: int = Field(..., description="最大并发数")
    current_running: int = Field(..., description="当前运行中的任务数")
    available_slots: int = Field(..., description="可用的并发槽位数")


class UpdateConcurrencyRequest(BaseModel):
    """更新并发数请求schema"""
    max_concurrency: int = Field(..., ge=1, le=100, description="新的最大并发数（1-100）")


class UpdateConcurrencyResponse(BaseModel):
    """更新并发数响应schema"""
    success: bool = Field(..., description="是否更新成功")
    message: str = Field(..., description="响应消息")
    old_limit: int = Field(..., description="旧的并发数限制")
    new_limit: int = Field(..., description="新的并发数限制")


@router.get("/info", response_model=ConcurrencyInfoResponse)
def get_concurrency_info():
    """获取当前并发控制信息"""
    try:
        info = get_current_concurrency_info()
        return ConcurrencyInfoResponse(**info)
    except Exception as e:
        logger.error(f"获取并发控制信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取信息失败: {str(e)}")


@router.post("/update", response_model=UpdateConcurrencyResponse)
def update_concurrency_limit(request: UpdateConcurrencyRequest):
    """动态更新最大并发数"""
    try:
        # 获取当前限制
        current_info = get_current_concurrency_info()
        old_limit = current_info["max_concurrency"]
        
        # 更新并发数限制
        success = update_max_concurrency(request.max_concurrency)
        
        if success:
            logger.info(f"并发数限制已更新: {old_limit} -> {request.max_concurrency}")
            return UpdateConcurrencyResponse(
                success=True,
                message=f"并发数限制已成功更新为 {request.max_concurrency}",
                old_limit=old_limit,
                new_limit=request.max_concurrency
            )
        else:
            raise HTTPException(status_code=400, detail="更新并发数限制失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新并发数限制失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")


@router.get("/health")
def concurrency_health_check():
    """并发控制健康检查"""
    try:
        info = get_current_concurrency_info()
        
        # 计算使用率
        usage_rate = (info["current_running"] / info["max_concurrency"]) * 100
        
        # 判断健康状态
        if usage_rate < 70:
            status = "healthy"
            message = "并发控制运行正常"
        elif usage_rate < 90:
            status = "warning"
            message = "并发使用率较高，建议关注"
        else:
            status = "critical"
            message = "并发使用率过高，可能影响性能"
        
        return {
            "status": status,
            "message": message,
            "usage_rate": round(usage_rate, 2),
            "concurrency_info": info
        }
        
    except Exception as e:
        logger.error(f"并发控制健康检查失败: {str(e)}")
        return {
            "status": "error",
            "message": f"健康检查失败: {str(e)}",
            "usage_rate": 0,
            "concurrency_info": {}
        }
