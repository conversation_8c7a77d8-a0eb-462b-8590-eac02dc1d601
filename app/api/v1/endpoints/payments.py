"""
支付管理API端点
"""
from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import payment, performance
from app.schemas.payment import (
    Payment,
    PaymentCreate,
    PaymentCreateWithFile,
    PaymentCreateInternal,
    PaymentUpdate,
    PaymentMarkAsPaid,
    PaymentSearch,
    PaymentListResponse,
    PaymentStatsResponse
)
from app.utils.file_handler import FileHandler

router = APIRouter()


def _convert_payment_model_to_schema(payment_model, post_link: str) -> Payment:
    """将数据库模型转换为 API schema"""
    return Payment(
        id=payment_model.id,
        post_link=post_link,
        payment_amount=payment_model.payment_amount,
        paypal_accounts=payment_model.paypal_accounts,
        tracker=payment_model.tracker,
        fund_source=payment_model.fund_source,
        payment_screenshot_filename=payment_model.payment_screenshot_filename,
        note=payment_model.note,
        payout_date=payment_model.payout_date,
        created_at=payment_model.created_at,
        updated_at=payment_model.updated_at
    )


def _convert_payments_list(db: Session, payments_list) -> List[Payment]:
    """批量转换支付记录列表"""
    converted_payments = []
    for payment_obj in payments_list:
        performance_obj = performance.get(db, id=payment_obj.performance_id)
        post_link = performance_obj.post_link if performance_obj else f"unknown_performance_{payment_obj.performance_id}"
        converted_payments.append(_convert_payment_model_to_schema(payment_obj, post_link))
    return converted_payments


@router.get("/", response_model=PaymentListResponse)
def get_payments(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    performance_id: Optional[int] = Query(None, description="绩效记录ID"),
    tracker: Optional[str] = Query(None, description="跟进人"),
    has_payout_date: Optional[bool] = Query(None, description="是否已支付"),
    db: Session = Depends(get_db)
):
    """获取支付记录列表"""
    if performance_id:
        payments = payment.get_by_performance(db, performance_id=performance_id, skip=skip, limit=limit)
        total = len(payment.get_by_performance(db, performance_id=performance_id, skip=0, limit=10000))
    elif tracker:
        payments = payment.get_by_tracker(db, tracker=tracker, skip=skip, limit=limit)
        total = len(payment.get_by_tracker(db, tracker=tracker, skip=0, limit=10000))
    elif has_payout_date is not None:
        if has_payout_date:
            payments = payment.get_completed_payments(db, skip=skip, limit=limit)
            total = len(payment.get_completed_payments(db, skip=0, limit=10000))
        else:
            payments = payment.get_pending_payments(db, limit=limit)
            total = len(payments)
    else:
        payments = payment.get_multi(db, skip=skip, limit=limit)
        total = len(payment.get_multi(db, skip=0, limit=10000))

    # 转换模型为 schema
    converted_payments = _convert_payments_list(db, payments)

    return PaymentListResponse(
        items=converted_payments,
        total=total,
        skip=skip,
        limit=limit
    )


@router.post("/search", response_model=PaymentListResponse)
def search_payments(
    search_params: PaymentSearch,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db)
):
    """搜索支付记录"""
    payments = payment.search_payments(
        db,
        performance_id=search_params.performance_id,
        tracker=search_params.tracker,
        paypal_account=search_params.paypal_account,
        fund_source=search_params.fund_source,
        min_amount=search_params.min_amount,
        max_amount=search_params.max_amount,
        has_payout_date=search_params.has_payout_date,
        skip=skip,
        limit=limit
    )
    
    # 计算总数
    total = len(payment.search_payments(
        db,
        performance_id=search_params.performance_id,
        tracker=search_params.tracker,
        paypal_account=search_params.paypal_account,
        fund_source=search_params.fund_source,
        min_amount=search_params.min_amount,
        max_amount=search_params.max_amount,
        has_payout_date=search_params.has_payout_date,
        skip=0,
        limit=10000
    ))
    
    # 转换模型为 schema
    converted_payments = _convert_payments_list(db, payments)

    return PaymentListResponse(
        items=converted_payments,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/stats", response_model=PaymentStatsResponse)
def get_payment_stats(db: Session = Depends(get_db)):
    """获取支付统计信息"""
    stats = payment.get_payment_statistics(db)
    return PaymentStatsResponse(**stats)


@router.get("/pending", response_model=List[Payment])
def get_pending_payments(
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db)
):
    """获取待支付的记录"""
    payments = payment.get_pending_payments(db, limit=limit)
    # 转换模型为 schema
    return _convert_payments_list(db, payments)


@router.get("/completed", response_model=PaymentListResponse)
def get_completed_payments(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db)
):
    """获取已完成的支付记录"""
    payments = payment.get_completed_payments(db, skip=skip, limit=limit)
    total = len(payment.get_completed_payments(db, skip=0, limit=10000))

    # 转换模型为 schema
    converted_payments = _convert_payments_list(db, payments)

    return PaymentListResponse(
        items=converted_payments,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/date-range", response_model=PaymentListResponse)
def get_payments_by_date_range(
    start_date: datetime = Query(..., description="开始日期"),
    end_date: datetime = Query(..., description="结束日期"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db)
):
    """根据支付日期范围获取支付记录"""
    payments = payment.get_by_date_range(
        db,
        start_date=start_date,
        end_date=end_date,
        skip=skip,
        limit=limit
    )
    
    total = len(payment.get_by_date_range(
        db,
        start_date=start_date,
        end_date=end_date,
        skip=0,
        limit=10000
    ))

    # 转换模型为 schema
    converted_payments = _convert_payments_list(db, payments)

    return PaymentListResponse(
        items=converted_payments,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/amount-range", response_model=PaymentListResponse)
def get_payments_by_amount_range(
    min_amount: Optional[Decimal] = Query(None, description="最小金额", ge=0),
    max_amount: Optional[Decimal] = Query(None, description="最大金额", ge=0),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db)
):
    """根据支付金额范围获取支付记录"""
    payments = payment.get_by_amount_range(
        db,
        min_amount=min_amount,
        max_amount=max_amount,
        skip=skip,
        limit=limit
    )
    
    total = len(payment.get_by_amount_range(
        db,
        min_amount=min_amount,
        max_amount=max_amount,
        skip=0,
        limit=10000
    ))

    # 转换模型为 schema
    converted_payments = _convert_payments_list(db, payments)

    return PaymentListResponse(
        items=converted_payments,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/by-paypal/{paypal_account}", response_model=PaymentListResponse)
def get_payments_by_paypal_account(
    paypal_account: str,
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    db: Session = Depends(get_db)
):
    """根据PayPal账户获取支付记录"""
    payments = payment.get_by_paypal_account(
        db,
        paypal_account=paypal_account,
        skip=skip,
        limit=limit
    )
    
    total = len(payment.get_by_paypal_account(
        db,
        paypal_account=paypal_account,
        skip=0,
        limit=10000
    ))

    # 转换模型为 schema
    converted_payments = _convert_payments_list(db, payments)

    return PaymentListResponse(
        items=converted_payments,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{payment_id}", response_model=Payment)
def get_payment(
    payment_id: int,
    db: Session = Depends(get_db)
):
    """根据支付ID获取支付详情"""
    db_payment = payment.get(db, id=payment_id)
    if not db_payment:
        raise HTTPException(status_code=404, detail="支付记录不存在")

    # 查找对应的 post_link
    performance_obj = performance.get(db, id=db_payment.performance_id)
    post_link = performance_obj.post_link if performance_obj else f"unknown_performance_{db_payment.performance_id}"

    return _convert_payment_model_to_schema(db_payment, post_link)


@router.post("/", response_model=Payment)
def create_payment(
    payment_in: PaymentCreate,
    db: Session = Depends(get_db)
):
    """创建新支付记录"""
    # 根据 post_link 查找对应的 performance_id
    performance_obj = performance.get_by_post_link(db, post_link=payment_in.post_link)
    if not performance_obj:
        raise HTTPException(status_code=404, detail=f"未找到对应的绩效记录: {payment_in.post_link}")

    # 创建内部使用的 PaymentCreateInternal 对象
    internal_payment_data = PaymentCreateInternal(
        performance_id=performance_obj.id,
        payment_amount=payment_in.payment_amount,
        paypal_accounts=payment_in.paypal_accounts,
        tracker=payment_in.tracker,
        fund_source=payment_in.fund_source,
        payment_screenshot_filename=payment_in.payment_screenshot_filename,
        note=payment_in.note
    )

    created_payment = payment.create_internal(db, obj_in=internal_payment_data)

    # 返回时将 performance_id 替换为 post_link
    return _convert_payment_model_to_schema(created_payment, payment_in.post_link)


@router.post("/with-file", response_model=Payment)
async def create_payment_with_file(
    post_link: str = Form(..., description="帖子链接"),
    payment_amount: Optional[Decimal] = Form(None, description="支付金额"),
    paypal_accounts: Optional[str] = Form(None, description="PayPal账户"),
    tracker: Optional[str] = Form(None, description="跟进人"),
    fund_source: Optional[str] = Form(None, description="资金来源"),
    note: Optional[str] = Form(None, description="备注"),
    payment_screenshot_file: Optional[UploadFile] = File(None, description="支付截图文件"),
    db: Session = Depends(get_db)
):
    """创建新支付记录（支持文件上传）"""
    # 根据 post_link 查找对应的 performance_id
    performance_obj = performance.get_by_post_link(db, post_link=post_link)
    if not performance_obj:
        raise HTTPException(status_code=404, detail=f"未找到对应的绩效记录: {post_link}")

    # 处理文件上传
    screenshot_filename = None
    if payment_screenshot_file:
        screenshot_filename = await FileHandler.save_payment_screenshot(payment_screenshot_file)

    try:
        # 创建内部使用的 PaymentCreateInternal 对象
        internal_payment_data = PaymentCreateInternal(
            performance_id=performance_obj.id,
            payment_amount=payment_amount,
            paypal_accounts=paypal_accounts,
            tracker=tracker,
            fund_source=fund_source,
            payment_screenshot_filename=screenshot_filename,
            note=note
        )

        created_payment = payment.create_internal(db, obj_in=internal_payment_data)

        # 返回时将 performance_id 替换为 post_link
        return _convert_payment_model_to_schema(created_payment, post_link)

    except Exception as e:
        # 如果创建支付记录失败，删除已上传的文件
        if screenshot_filename:
            FileHandler.delete_payment_screenshot(screenshot_filename)
        raise HTTPException(status_code=500, detail=f"创建支付记录失败: {str(e)}")


@router.put("/{payment_id}", response_model=Payment)
def update_payment(
    payment_id: int,
    payment_in: PaymentUpdate,
    db: Session = Depends(get_db)
):
    """更新支付记录"""
    db_payment = payment.get(db, id=payment_id)
    if not db_payment:
        raise HTTPException(status_code=404, detail="支付记录不存在")

    updated_payment = payment.update(db, db_obj=db_payment, obj_in=payment_in)

    # 查找对应的 post_link
    performance_obj = performance.get(db, id=updated_payment.performance_id)
    post_link = performance_obj.post_link if performance_obj else f"unknown_performance_{updated_payment.performance_id}"

    return _convert_payment_model_to_schema(updated_payment, post_link)


@router.patch("/{payment_id}/mark-as-paid", response_model=Payment)
def mark_payment_as_paid(
    payment_id: int,
    mark_paid: PaymentMarkAsPaid,
    db: Session = Depends(get_db)
):
    """标记为已支付"""
    db_payment = payment.mark_as_paid(
        db,
        payment_id=payment_id,
        payout_date=mark_paid.payout_date
    )
    if not db_payment:
        raise HTTPException(status_code=404, detail="支付记录不存在")

    # 查找对应的 post_link
    performance_obj = performance.get(db, id=db_payment.performance_id)
    post_link = performance_obj.post_link if performance_obj else f"unknown_performance_{db_payment.performance_id}"

    return _convert_payment_model_to_schema(db_payment, post_link)


@router.delete("/{payment_id}", response_model=Payment)
def delete_payment(
    payment_id: int,
    db: Session = Depends(get_db)
):
    """删除支付记录"""
    db_payment = payment.get(db, id=payment_id)
    if not db_payment:
        raise HTTPException(status_code=404, detail="支付记录不存在")

    # 查找对应的 post_link
    performance_obj = performance.get(db, id=db_payment.performance_id)
    post_link = performance_obj.post_link if performance_obj else f"unknown_performance_{db_payment.performance_id}"

    # 删除关联的文件
    if deleted_payment.payment_screenshot_filename:
        FileHandler.delete_payment_screenshot(deleted_payment.payment_screenshot_filename)

    deleted_payment = payment.remove(db, id=payment_id)
    return _convert_payment_model_to_schema(deleted_payment, post_link)


@router.get("/{payment_id}/screenshot")
async def download_payment_screenshot(
    payment_id: int,
    db: Session = Depends(get_db)
):
    """下载支付截图文件"""
    # 获取支付记录
    db_payment = payment.get(db, id=payment_id)
    if not db_payment:
        raise HTTPException(status_code=404, detail="支付记录不存在")

    # 检查是否有截图文件
    if not db_payment.payment_screenshot_filename:
        raise HTTPException(status_code=404, detail="该支付记录没有截图文件")

    # 获取文件路径
    file_path = FileHandler.get_payment_screenshot_path(db_payment.payment_screenshot_filename)
    if not file_path:
        raise HTTPException(status_code=404, detail="截图文件不存在")

    # 获取Content-Type
    content_type = FileHandler.get_file_content_type(db_payment.payment_screenshot_filename)

    # 返回文件
    return FileResponse(
        path=file_path,
        media_type=content_type,
        filename=db_payment.payment_screenshot_filename
    )
