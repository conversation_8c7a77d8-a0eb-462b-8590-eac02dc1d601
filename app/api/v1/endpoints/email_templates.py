"""
邮件模板管理API端点
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db
from app.crud import email_template
from app.schemas.email_template import (
    EmailTemplate,
    EmailTemplateCreate,
    EmailTemplateUpdate,
    EmailTemplateListResponse,
    EmailTemplateUsageResponse,
    EmailTemplateWithUsage
)

router = APIRouter()


@router.get("/", response_model=EmailTemplateListResponse)
def get_email_templates(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    project_code: Optional[str] = Query(None, description="项目编码"),
    name_pattern: Optional[str] = Query(None, description="模板名称模糊搜索"),
    db: Session = Depends(get_db)
):
    """获取邮件模板列表"""
    if name_pattern:
        templates = email_template.get_by_name_pattern(
            db, name_pattern=name_pattern, project_code=project_code, skip=skip, limit=limit
        )
        total = len(email_template.get_by_name_pattern(
            db, name_pattern=name_pattern, project_code=project_code, skip=0, limit=10000
        ))
    elif project_code:
        templates = email_template.get_by_project(
            db, project_code=project_code, skip=skip, limit=limit
        )
        total = len(email_template.get_by_project(
            db, project_code=project_code, skip=0, limit=10000
        ))
    else:
        templates = email_template.get_multi(db, skip=skip, limit=limit)
        total = len(email_template.get_multi(db, skip=0, limit=10000))
    
    return EmailTemplateListResponse(
        items=templates,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/usage/{project_code}", response_model=EmailTemplateUsageResponse)
def get_templates_with_usage(
    project_code: str,
    db: Session = Depends(get_db)
):
    """获取项目的邮件模板使用统计"""
    templates_with_usage = email_template.get_templates_with_usage(db, project_code=project_code)
    
    items = [
        EmailTemplateWithUsage(
            **item["template"].__dict__,
            usage_count=item["usage_count"]
        )
        for item in templates_with_usage
    ]
    
    return EmailTemplateUsageResponse(
        items=items,
        total=len(items)
    )


@router.get("/{template_id}", response_model=EmailTemplate)
def get_email_template(
    template_id: int,
    db: Session = Depends(get_db)
):
    """根据模板ID获取邮件模板详情"""
    db_template = email_template.get(db, id=template_id)
    if not db_template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")
    return db_template


@router.post("/", response_model=EmailTemplate)
def create_email_template(
    template_in: EmailTemplateCreate,
    db: Session = Depends(get_db)
):
    """创建新邮件模板"""
    try:
        return email_template.create_template(db, obj_in=template_in)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/{template_id}", response_model=EmailTemplate)
def update_email_template(
    template_id: int,
    template_in: EmailTemplateUpdate,
    db: Session = Depends(get_db)
):
    """更新邮件模板"""
    db_template = email_template.update_template(db, template_id=template_id, obj_in=template_in)
    if not db_template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")
    return db_template


@router.delete("/{template_id}", response_model=EmailTemplate)
def delete_email_template(
    template_id: int,
    db: Session = Depends(get_db)
):
    """删除邮件模板"""
    db_template = email_template.delete_template(db, template_id=template_id)
    if not db_template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")
    return db_template
