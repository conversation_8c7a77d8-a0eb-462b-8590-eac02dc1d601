"""
项目表模型
"""
from typing import List, Optional
from sqlalchemy import String, Text, ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel


class Project(BaseModel):
    """项目表模型"""
    __tablename__ = "projects"

    code: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        comment="项目唯一标识符"
    )
    
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="项目名称"
    )

    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="项目描述"
    )

    email_domain: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="项目邮箱域名"
    )

    email_label: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        default=[],
        nullable=True,
        comment="项目邮箱标签"
    )

    email_sublabel: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(String),
        default=[],
        nullable=True,
        comment="项目邮箱子标签"
    )

    gmail_token_path: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="Gmail令牌文件路径"
    )

    tracker: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="项目负责人"
    )

    # 关联关系
    email_templates: Mapped[List["EmailTemplate"]] = relationship(
        "EmailTemplate",
        back_populates="project",
        cascade="all, delete-orphan"
    )
    
    crawler_tasks: Mapped[List["CrawlerTask"]] = relationship(
        "CrawlerTask",
        back_populates="project",
        cascade="all, delete-orphan"
    )
    
    kols: Mapped[List["KOL"]] = relationship(
        "KOL",
        back_populates="project",
        cascade="all, delete-orphan"
    )
    
    email_send_logs: Mapped[List["EmailSend"]] = relationship(
        "EmailSend",
        back_populates="project",
        cascade="all, delete-orphan"
    )
    
    candidates: Mapped[List["Candidate"]] = relationship(
        "Candidate",
        back_populates="project",
        cascade="all, delete-orphan"
    )
    
    performances: Mapped[List["Performance"]] = relationship(
        "Performance",
        back_populates="project",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return f"<Project(code='{self.code}', name='{self.name}')>"
