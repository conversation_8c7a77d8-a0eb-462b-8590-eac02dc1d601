"""
邮件模板表模型
"""
from typing import List, Optional
from sqlalchemy import String, Text, ForeignKey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel


class EmailTemplate(BaseModel):
    """邮件模板表模型"""
    __tablename__ = "email_templates"

    id: Mapped[int] = mapped_column(
        primary_key=True,
        autoincrement=True,
        comment="主键ID"
    )

    code: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="模板唯一代码"
    )
    
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="模板名称"
    )
    
    project_code: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("projects.code", ondelete="CASCADE"),
        nullable=False,
        comment="项目编码"
    )
    
    postmark_token: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="邮件服务提供商的令牌"
    )
    
    from_email: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="发件人邮箱"
    )
    
    note: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="备注"
    )

    # 表级约束
    __table_args__ = (
        UniqueConstraint('code', 'project_code', name='uk_email_templates_code_project_code'),
    )

    # 关联关系
    project: Mapped["Project"] = relationship(
        "Project",
        back_populates="email_templates"
    )
    
    email_send_logs: Mapped[List["EmailSend"]] = relationship(
        "EmailSend",
        back_populates="email_template",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return f"<EmailTemplate(code='{self.code}', name='{self.name}')>"
