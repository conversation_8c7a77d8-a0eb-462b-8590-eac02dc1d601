"""
爬虫任务表模型
"""
from typing import List, Optional, Dict, Any
from decimal import Decimal
from sqlalchemy import String, Text, ForeignKey, Enum, Integer, DECIMAL, SmallInteger
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import JSONB

from app.models.base import BaseModel
from app.models.enums import SourceEnum, PlatformEnum, CrawlerTaskStatusEnum


class CrawlerTask(BaseModel):
    """爬虫任务表模型"""
    __tablename__ = "crawler_tasks"

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=True,
        comment="主键ID"
    )
    
    task_name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="任务名称"
    )
    
    source: Mapped[str] = mapped_column(
        Enum(SourceEnum),
        nullable=False,
        comment="数据源"
    )
    
    platform: Mapped[PlatformEnum] = mapped_column(
        Enum(PlatformEnum),
        nullable=False,
        comment="爬取平台"
    )
    
    project_code: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("projects.code", ondelete="CASCADE"),
        nullable=False,
        comment="项目编码"
    )
    
    status: Mapped[CrawlerTaskStatusEnum] = mapped_column(
        Enum(CrawlerTaskStatusEnum),
        default=CrawlerTaskStatusEnum.PENDING,
        nullable=False,
        comment="任务状态"
    )
    
    filters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB,
        default={},
        nullable=True,
        comment="过滤条件JSON"
    )
    
    cookies: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="爬虫cookie"
    )
    
    log_msg: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="日志信息，包含爬取条数crawl_count、有邮箱的条数has_email_count，执行结果信息"
    )

    total_duration: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(10, 2),
        nullable=True,
        comment="任务总体耗时（秒）"
    )

    task_progress: Mapped[int] = mapped_column(
        SmallInteger,
        default=0,
        nullable=False,
        comment="任务进度（0-100）"
    )

    # 关联关系
    project: Mapped["Project"] = relationship(
        "Project",
        back_populates="crawler_tasks"
    )

    kols: Mapped[List["KOL"]] = relationship(
        "KOL",
        back_populates="crawler_task"
    )

    def __repr__(self) -> str:
        return f"<CrawlerTask(id={self.id}, task_name='{self.task_name}', status='{self.status}')>"
