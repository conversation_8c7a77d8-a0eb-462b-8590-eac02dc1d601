"""
绩效表模型
"""
from typing import List, Optional
from datetime import datetime
from sqlalchemy import String, ForeignKey, Enum, Integer, BigInteger, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel
from app.models.enums import PlatformEnum


class Performance(BaseModel):
    """绩效表模型"""
    __tablename__ = "performance"

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=True,
        comment="主键ID"
    )
    
    platform: Mapped[PlatformEnum] = mapped_column(
        Enum(PlatformEnum),
        nullable=False,
        comment="KOL所在平台"
    )

    social_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="KOL的社交媒体ID"
    )

    kol_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("kols.id", ondelete="CASCADE"),
        nullable=False,
        comment="KOL ID（外键）"
    )
    
    project_code: Mapped[str] = mapped_column(
        String(50),
        Foreign<PERSON>ey("projects.code", ondelete="CASCADE"),
        nullable=False,
        comment="项目编码"
    )
    

    
    post_link: Mapped[str] = mapped_column(
        String(500),
        unique=True,
        nullable=False,
        comment="帖子链接"
    )
    
    post_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="发布日期"
    )
    
    # 总数据
    views_total: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="总观看数"
    )
    
    likes_total: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="总点赞数"
    )
    
    comments_total: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="总评论数"
    )
    
    shares_total: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="总分享数"
    )
    
    # 第一天数据
    views_day1: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第一天观看数"
    )
    
    likes_day1: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第一天点赞数"
    )
    
    comments_day1: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第一天评论数"
    )
    
    shares_day1: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第一天分享数"
    )
    
    # 第三天数据
    views_day3: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第三天观看数"
    )
    
    likes_day3: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第三天点赞数"
    )
    
    comments_day3: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第三天评论数"
    )
    
    shares_day3: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第三天分享数"
    )
    
    # 第七天数据
    views_day7: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第七天观看数"
    )
    
    likes_day7: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第七天点赞数"
    )
    
    comments_day7: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第七天评论数"
    )
    
    shares_day7: Mapped[Optional[int]] = mapped_column(
        BigInteger,
        nullable=True,
        comment="第七天分享数"
    )
    


    # 关联关系
    kol: Mapped["KOL"] = relationship(
        "KOL",
        back_populates="performances"
    )
    
    project: Mapped["Project"] = relationship(
        "Project",
        back_populates="performances"
    )
    
    payments: Mapped[List["Payment"]] = relationship(
        "Payment",
        back_populates="performance",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return f"<Performance(id={self.id}, kol_id='{self.kol_id}', post_link='{self.post_link}')>"
