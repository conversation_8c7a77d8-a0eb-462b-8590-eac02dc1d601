"""
候选人表模型
"""
from typing import Optional
from datetime import datetime
from sqlalchemy import String, Text, ForeignKey, Enum, Integer, DateTime, Boolean, JSON, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel
from app.models.enums import PlatformEnum, FollowUpStatusEnum


class Candidate(BaseModel):
    """候选人表模型"""
    __tablename__ = "candidates"

    # 索引定义
    __table_args__ = (
        # 基础索引
        Index('idx_candidates_kol_id', 'kol_id'),
        Index('idx_candidates_project_code', 'project_code'),
        Index('idx_candidates_follow_up_status', 'follow_up_status'),
        Index('idx_candidates_tracker', 'tracker'),
        Index('idx_candidates_reply_email', 'reply_email_addr'),
        # 复合索引
        Index('idx_candidates_project_status', 'project_code', 'follow_up_status'),
        Index('idx_candidates_tracker_status', 'tracker', 'follow_up_status'),
        # Gmail同步相关索引
        Index('idx_candidates_need_review', 'need_review'),
        Index('idx_candidates_project_need_review', 'project_code', 'need_review'),
    )

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=True,
        comment="主键ID"
    )
    
    platform: Mapped[Optional[PlatformEnum]] = mapped_column(
        Enum(PlatformEnum),
        nullable=True,
        comment="KOL所在平台（候选人阶段可能未知）"
    )

    kol_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="KOL ID（外键，可为空）或UUID字符串（需要审核时）"
    )
    
    social_id: Mapped[Optional[str]] = mapped_column(
        "social_id",
        String(255),
        nullable=True,
        comment="KOL的社交媒体ID"
    )

    nick_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="KOL的昵称"
    )
    
    project_code: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("projects.code", ondelete="CASCADE"),
        nullable=False,
        comment="项目编码"
    )
    
    reply_email_addr: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="回复邮箱地址"
    )
    
    follow_up_status: Mapped[Optional[FollowUpStatusEnum]] = mapped_column(
        Enum(FollowUpStatusEnum),
        nullable=True,
        comment="跟进状态"
    )
    
    follow_up_note: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="跟进备注"
    )

    first_contact_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="首次联系时间"
    )

    last_contact_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="最近一次联系时间"
    )

    send_round: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="发送轮次"
    )

    latest_email_send_id: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("email_send_logs.id", ondelete="SET NULL"),
        nullable=True,
        comment="最新邮件发送ID"
    )
    
    thread_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="关联邮件线程ID"
    )
    
    tracker: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="分配给的跟进人"
    )
    
    note: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="备注"
    )

    # Gmail同步相关字段
    parsed_email: Mapped[Optional[str]] = mapped_column(
        JSON,
        nullable=True,
        comment="解析的原始邮箱数据（JSON格式）"
    )

    parsed_social_link: Mapped[Optional[str]] = mapped_column(
        JSON,
        nullable=True,
        comment="解析的原始社交媒体链接数据（JSON格式）"
    )

    need_review: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否需要人工审核"
    )

    # 关联关系 - 注意：kol_id现在可能是UUID字符串，所以不使用外键关系
    
    project: Mapped["Project"] = relationship(
        "Project",
        back_populates="candidates"
    )
    
    latest_email_send: Mapped[Optional["EmailSend"]] = relationship(
        "EmailSend",
        back_populates="candidates"
    )

    def __repr__(self) -> str:
        return f"<Candidate(id={self.id}, kol_id='{self.kol_id}', status='{self.follow_up_status}')>"
