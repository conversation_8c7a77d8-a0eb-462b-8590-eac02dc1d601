"""
KOL管理平台数据模型
基于v2数据库表结构重新设计的SQLAlchemy模型
"""

# 导入基础类和枚举
from app.models.base import Base, BaseModel, TimestampMixin
from app.models.enums import (
    PlatformEnum,
    CrawlerTaskStatusEnum,
    EmailSendStatusEnum,
    EmailFetchStatusEnum,
    FollowUpStatusEnum,
    PerformanceStatusEnum,
    KOLTierEnum,
    KOLActivityStatusEnum
)

# 导入所有模型
from app.models.project import Project
from app.models.email_template import EmailTemplate
from app.models.crawler_task import CrawlerTask
from app.models.kol import KOL
from app.models.email_send_logs import EmailSend
from app.models.candidate import Candidate
from app.models.performance import Performance
from app.models.payment import Payment


# 便于导入使用
__all__ = [
    # 基础类
    "Base",
    "BaseModel",
    "TimestampMixin",

    # 枚举类
    "PlatformEnum",
    "CrawlerTaskStatusEnum",
    "EmailSendStatusEnum",
    "FollowUpStatusEnum",
    "PerformanceStatusEnum",
    "KOLTierEnum",
    "KOLActivityStatusEnum",

    # 模型类
    "Project",
    "EmailTemplate",
    "CrawlerTask",
    "KOL",
    "EmailSend",
    "Candidate",
    "Performance",
    "Payment",

]
