"""
邮件发送表模型
"""
from typing import List, Optional
from datetime import datetime
from sqlalchemy import String, Text, ForeignKey, Enum, Integer, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import BaseModel
from app.models.enums import PlatformEnum, EmailSendStatusEnum


class EmailSend(BaseModel):
    """邮件发送表模型"""
    __tablename__ = "email_send_logs"

    id: Mapped[int] = mapped_column(
        Integer,
        primary_key=True,
        autoincrement=True,
        comment="主键ID"
    )
    
    platform: Mapped[Optional[PlatformEnum]] = mapped_column(
        Enum(PlatformEnum),
        nullable=True,
        comment="邮件发送的平台"
    )
    
    kol_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("kols.id", ondelete="SET NULL"),
        nullable=False,
        comment="KOL ID（外键）"
    )
    
    send_status: Mapped[EmailSendStatusEnum] = mapped_column(
        Enum(EmailSendStatusEnum),
        default=EmailSendStatusEnum.PENDING,
        nullable=False,
        comment="邮件发送状态"
    )
    
    send_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="邮件发送时间"
    )
    
    project_code: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("projects.code", ondelete="CASCADE"),
        nullable=False,
        comment="项目编码"
    )
    
    template_code: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("email_templates.code", ondelete="CASCADE"),
        nullable=False,
        comment="邮件模板代码"
    )
    
    from_email: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="发件人邮箱"
    )
    
    to_email: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="收件人邮箱"
    )
    
    note: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="备注"
    )

    # 关联关系
    kol: Mapped["KOL"] = relationship(
        "KOL",
        back_populates="email_send_logs"
    )
    
    project: Mapped["Project"] = relationship(
        "Project",
        back_populates="email_send_logs"
    )
    
    email_template: Mapped["EmailTemplate"] = relationship(
        "EmailTemplate",
        back_populates="email_send_logs"
    )
    
    candidates: Mapped[List["Candidate"]] = relationship(
        "Candidate",
        back_populates="latest_email_send"
    )

    def __repr__(self) -> str:
        return f"<EmailSend(id={self.id}, kol_id='{self.kol_id}', status='{self.send_status}')>"
