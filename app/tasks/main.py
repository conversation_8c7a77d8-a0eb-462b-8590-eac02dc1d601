"""
任务管理主模块
提供任务工厂和便捷的任务执行函数
"""
from typing import Dict, Any, Optional
from app.tasks.task import ModashCrawTask, TtoneCrawTask, TiktokCrawTask, InstagramCrawTask, ModashCrawlerTask, KOLDataUpdateTask
from app.logging_config import get_task_logger

logger = get_task_logger("task_main")


class TaskFactory:
    """任务工厂类"""

    # 任务类型映射
    TASK_CLASSES = {
        "modash": ModashCrawTask,
        "ttone": TtoneCrawTask,
        "tiktok": TiktokCrawTask,
        "instagram": InstagramCrawTask,
        "data_collector_v3": ModashCrawlerTask,
        "kol_data_update": KOLDataUpdateTask,
    }

    @classmethod
    def create_task(cls, platform: str, shared_data: Optional[Dict] = None):
        """创建任务实例"""
        task_class = cls.TASK_CLASSES.get(platform)
        if not task_class:
            raise ValueError(f"不支持的平台类型: {platform}")

        return task_class(shared_data)

    @classmethod
    def get_supported_platforms(cls):
        """获取支持的平台列表"""
        return list(cls.TASK_CLASSES.keys())


def run_modash_crawl(cookie: str, request_body: dict, platform: str, project_code: str = "DEFAULT"):
    """运行Modash爬虫任务"""
    try:
        task = TaskFactory.create_task("modash")
        result = task.run_batch_search(cookie, request_body, platform, project_code)

        logger.info(f"Modash crawl completed: {result}")
        return result

    except Exception as e:
        logger.error(f"Modash crawl failed: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "platform": platform
        }


def run_tiktok_crawl(url: str, max_videos: int = 20, project_code: str = "DEFAULT"):
    """运行TikTok爬虫任务"""
    try:
        task = TaskFactory.create_task("tiktok")
        result = task.run_single_crawl(url, max_videos, project_code)

        logger.info(f"TikTok crawl completed: {result}")
        return result

    except Exception as e:
        logger.error(f"TikTok crawl failed: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "url": url
        }


def run_instagram_crawl(username_or_url: str, max_reels: int = 50, project_code: str = "DEFAULT"):
    """运行Instagram爬虫任务"""
    try:
        task = TaskFactory.create_task("instagram")
        result = task.run_single_crawl(username_or_url, max_reels, project_code)

        logger.info(f"Instagram crawl completed: {result}")
        return result

    except Exception as e:
        logger.error(f"Instagram crawl failed: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "username_or_url": username_or_url
        }


def run_task(platform: str, **kwargs):
    """通用任务运行函数"""
    if platform == "modash":
        return run_modash_crawl(
            kwargs.get("cookie"),
            kwargs.get("request_body"),
            kwargs.get("platform"),
            kwargs.get("project_code", "DEFAULT")
        )
    elif platform == "tiktok":
        return run_tiktok_crawl(
            kwargs.get("url"),
            kwargs.get("max_videos", 20),
            kwargs.get("project_code", "DEFAULT")
        )
    elif platform == "instagram":
        return run_instagram_crawl(
            kwargs.get("username_or_url"),
            kwargs.get("max_reels", 50),
            kwargs.get("project_code", "DEFAULT")
        )
    else:
        raise ValueError(f"不支持的平台类型: {platform}")


def run_data_collector_v3(task_name: str, source: str, platform: str,
                         project_code: str, filters: dict = None, cookies: str = None,
                         **kwargs):
    """运行数据采集器V3任务"""
    try:
        task = TaskFactory.create_task("data_collector_v3")
        result = task.run_data_collection(
            task_name=task_name,
            source=source,
            platform=platform,
            project_code=project_code,
            filters=filters,
            cookies=cookies,
            **kwargs
        )

        logger.info(f"数据采集器V3任务完成: {result}")
        return result

    except Exception as e:
        logger.error(f"数据采集器V3任务失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "task_name": task_name,
            "source": source,
            "platform": platform
        }


def run_kol_data_update(project_code: str = None, batch_size: int = 50,
                       pending_payments: list = None, **kwargs):
    """运行KOL数据更新任务"""
    try:
        task = TaskFactory.create_task("kol_data_update")
        result = task.run_data_update(
            project_code=project_code,
            batch_size=batch_size,
            pending_payments=pending_payments,
            **kwargs
        )

        logger.info(f"KOL数据更新任务完成: {result}")
        return result

    except Exception as e:
        logger.error(f"KOL数据更新任务失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "project_code": project_code
        }


# 向后兼容的函数
def generate_tasks(platform: str):
    """向后兼容的任务生成函数"""
    logger.warning("generate_tasks函数已废弃，请使用run_task函数")
    return run_task(platform)