"""
异步任务执行器
用于异步执行爬虫任务并实时推送进度
"""
import asyncio
import time
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.crud import crawler_task
from app.models.enums import CrawlerTaskStatusEnum
from app.websocket.task_manager import task_websocket_manager
from app.tasks.task import ModashCrawlerTask
from app.tasks.step import TaskCancelledException
from app.logging_config import get_task_logger

logger = get_task_logger("async_task_executor")


class AsyncTaskExecutor:
    """异步任务执行器"""

    def __init__(self):
        self.running_tasks: Dict[int, asyncio.Task] = {}
        self.cancelled_tasks: set = set()  # 记录被取消的任务ID，用户主动取消的，如果不在这里面那么则是由于其他异常导致的取消（Failed状态）
    
    async def execute_crawler_task(self, task_id: int, task_params: Dict[str, Any]):
        """异步执行爬虫任务"""
        start_time = time.time()
        db = SessionLocal()
        
        try:
            logger.info(f"开始执行异步爬虫任务: task_id={task_id}")
            
            # 更新任务状态为运行中（覆盖之前的日志）
            await self._update_task_progress(db, task_id, 0, "任务开始执行", CrawlerTaskStatusEnum.RUNNING, append_log=False)
            
            # 创建ModashCrawlerTask实例
            shared_data = {"validated_params": task_params, "crawler_task_id": task_id}
            crawler = ModashCrawlerTask(shared_data)
            
            # 设置进度回调
            crawler.progress_callback = lambda progress, msg: asyncio.create_task(
                self._update_task_progress(db, task_id, progress, msg, append_log=True)
            )
            
            # 执行任务的各个步骤
            await self._execute_task_steps(crawler, task_id, db)
            
            # 计算总耗时
            total_duration = time.time() - start_time
            
            # 任务完成（追加到日志历史）
            await self._update_task_progress(
                db, task_id, 100, "✅ 任务执行完成",
                CrawlerTaskStatusEnum.COMPLETED, total_duration, append_log=True
            )
            
            # 发送WebSocket完成消息
            await task_websocket_manager.send_completion_message(
                task_id, True, "✅ 任务执行完成", total_duration
            )
            
            logger.info(f"✅ 异步爬虫任务执行完成: task_id={task_id}, duration={total_duration:.2f}s")
            
        except (asyncio.CancelledError, TaskCancelledException):
            # 任务被取消
            total_duration = time.time() - start_time
            cancel_msg = "任务已被用户取消"

            logger.info(f"异步爬虫任务被取消: task_id={task_id}")

            # 只有在任务确实被标记为取消时才更新状态
            if task_id in self.cancelled_tasks:
                await self._update_task_progress(
                    db, task_id, None, cancel_msg,
                    CrawlerTaskStatusEnum.CANCELLED, total_duration, append_log=True
                )

                # 发送WebSocket取消消息
                await task_websocket_manager.send_completion_message(
                    task_id, False, cancel_msg, total_duration
                )

            # 重新抛出异常以确保任务正确结束
            raise

        except Exception as e:
            # 计算耗时
            total_duration = time.time() - start_time
            error_msg = f"❌ 任务执行失败: {str(e)}"
            # 打印详细的堆栈信息
            import traceback
            logger.error(f"❌ 异步爬虫任务执行失败: task_id={task_id}, error={str(e)}\n{traceback.format_exc()}")

            # 只有在任务未被取消时才更新为失败状态
            if task_id not in self.cancelled_tasks:
                await self._update_task_progress(
                    db, task_id, None, error_msg,
                    CrawlerTaskStatusEnum.FAILED, total_duration, append_log=True
                )

                # 发送WebSocket失败消息
                await task_websocket_manager.send_completion_message(
                    task_id, False, error_msg, total_duration
                )

        finally:
            db.close()
            # 清理运行中的任务记录
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            # 注意：不立即清理cancelled_tasks，保留一段时间供状态查询
            # cancelled_tasks会在一定时间后自动清理或在新任务启动时清理
    
    async def _execute_task_steps(self, crawler: ModashCrawlerTask, task_id: int, db: Session):
        """执行任务步骤"""
        total_steps = len(crawler.steps)

        for i, step_class in enumerate(crawler.steps):
            # 在每个步骤开始前检查取消状态
            if task_id in self.cancelled_tasks:
                logger.info(f"任务 {task_id} 在步骤 {step_class.__name__} 开始前被取消")
                raise TaskCancelledException(task_id, f"Task cancelled before step {step_class.__name__}")

            step_progress = int((i / total_steps) * 90)  # 预留10%给最终处理
            step_name = step_class.__name__

            logger.info(f"执行步骤: {step_name} (task_id={task_id})")

            # 更新进度
            await self._update_task_progress(
                db, task_id, step_progress, f"正在执行: {step_name}"
            )

            # 执行步骤
            if crawler.shared_data is None:
                crawler.shared_data = {}
            crawler.shared_data.update({"task_id": task_id})
            step_instance = step_class(crawler.shared_data)

            # 注入取消任务集合的引用
            step_instance.set_cancelled_tasks_ref(self.cancelled_tasks)

            try:
                # 使用带取消检查的执行方法
                await asyncio.to_thread(step_instance.run_with_cancellation_check)
                logger.info(f"步骤完成: {step_name} (task_id={task_id})")
            except TaskCancelledException:
                logger.info(f"步骤 {step_name} 被取消 (task_id={task_id})")
                raise  # 重新抛出取消异常

        # 最终处理前再次检查取消状态
        if task_id in self.cancelled_tasks:
            logger.info(f"任务 {task_id} 在最终处理前被取消")
            raise TaskCancelledException(task_id, "Task cancelled before final processing")

        # 最终处理
        await self._update_task_progress(
            db, task_id, 95, "正在完成最终处理..."
        )
    
    async def _update_task_progress(
        self,
        db: Session,
        task_id: int,
        progress: Optional[int],
        log_msg: str,
        status: Optional[CrawlerTaskStatusEnum] = None,
        total_duration: Optional[float] = None,
        append_log: bool = True
    ):
        """更新任务进度并发送WebSocket消息

        Args:
            db: 数据库会话
            task_id: 任务ID
            progress: 进度百分比
            log_msg: 日志消息
            status: 任务状态
            total_duration: 总耗时
            append_log: 是否追加日志消息（True=追加，False=覆盖）
        """
        try:
            # 获取当前任务以检查现有的log_msg
            current_task = await asyncio.to_thread(
                crawler_task.get,
                db,
                id=task_id
            )

            # 处理log_msg的追加逻辑
            final_log_msg = log_msg
            if append_log and current_task and current_task.log_msg:
                # 如果需要追加且已有log_msg，则追加新消息
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                final_log_msg = f"{current_task.log_msg}\n[{timestamp}] {log_msg}"

            # 更新数据库
            updated_task = await asyncio.to_thread(
                crawler_task.update_progress,
                db,
                task_id=task_id,
                progress=progress or 0,
                log_msg=final_log_msg,
                status=status,
                total_duration=total_duration
            )
            
            if updated_task and task_websocket_manager.is_connected(task_id):
                # 发送WebSocket消息（使用原始log_msg，不发送完整的历史记录）
                await task_websocket_manager.send_progress_update(
                    task_id=task_id,
                    progress=progress or 0,
                    log_msg=log_msg,  # 只发送当前消息，不发送完整历史
                    status=status.value if status else updated_task.status.value,
                    total_duration=total_duration or 0.0
                )
                
        except Exception as e:
            logger.error(f"更新任务进度失败: task_id={task_id}, error={str(e)}")
    
    async def start_task(self, task_id: int, task_params: Dict[str, Any]):
        """启动异步任务"""
        if task_id in self.running_tasks:
            logger.warning(f"任务已在运行中: task_id={task_id}")
            return False
        
        # 创建异步任务
        task = asyncio.create_task(self.execute_crawler_task(task_id, task_params))
        self.running_tasks[task_id] = task
        
        logger.info(f"异步任务已启动: task_id={task_id}")
        return True
    
    def is_task_running(self, task_id: int) -> bool:
        """检查任务是否正在运行"""
        return task_id in self.running_tasks and not self.running_tasks[task_id].done()

    def is_task_cancelled(self, task_id: int) -> bool:
        """检查任务是否已被取消"""
        return task_id in self.cancelled_tasks

    def get_running_task_ids(self) -> list:
        """获取所有正在运行的任务ID"""
        return list(self.running_tasks.keys())

    def get_cancelled_task_ids(self) -> list:
        """获取所有已取消的任务ID"""
        return list(self.cancelled_tasks)

    def clear_cancelled_task(self, task_id: int):
        """清理已取消的任务标记"""
        if task_id in self.cancelled_tasks:
            self.cancelled_tasks.remove(task_id)
            logger.info(f"已清理取消任务标记: task_id={task_id}")

    def cleanup_old_cancelled_tasks(self):
        """清理所有不再运行的已取消任务标记"""
        to_remove = []
        for task_id in self.cancelled_tasks:
            if task_id not in self.running_tasks:
                to_remove.append(task_id)

        for task_id in to_remove:
            self.cancelled_tasks.remove(task_id)

        if to_remove:
            logger.info(f"已清理 {len(to_remove)} 个旧的取消任务标记: {to_remove}")
    
    async def cancel_task(self, task_id: int) -> bool:
        """取消正在运行的任务"""
        if task_id not in self.running_tasks:
            logger.warning(f"任务 {task_id} 不在运行队列中，无法取消")
            return False

        logger.info(f"开始取消任务: task_id={task_id}")

        # 标记任务为已取消（这会被步骤检查到）
        self.cancelled_tasks.add(task_id)

        # 取消asyncio任务
        task = self.running_tasks[task_id]
        task.cancel()

        try:
            # 等待任务完成取消
            await asyncio.wait_for(task, timeout=30.0)  # 30秒超时
        except asyncio.CancelledError:
            logger.info(f"任务已成功取消: task_id={task_id}")
        except asyncio.TimeoutError:
            logger.warning(f"任务取消超时: task_id={task_id}")
            # 即使超时，任务仍然被标记为取消
        except TaskCancelledException:
            logger.info(f"任务通过步骤检查被取消: task_id={task_id}")
        except Exception as e:
            logger.error(f"任务取消过程中出现异常: task_id={task_id}, error={str(e)}")

        # 注意：不在这里删除running_tasks和cancelled_tasks
        # 这些会在execute_crawler_task的finally块中清理
        return True


# 全局异步任务执行器实例
async_task_executor = AsyncTaskExecutor()
