"""
批量数据保存步骤
批量保存KOL数据到数据库
"""
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from app.tasks.step import Step
from app.models.kol import KOL
from app.models.enums import PlatformEnum
from app.db.session import get_db
from app.logging_config import get_task_logger

logger = get_task_logger("batch_data_save_step")


class BatchSaveKOLDataStep(Step):
    """批量保存KOL数据步骤"""
    
    def run(self):
        """批量保存KOL数据到数据库"""
        filtered_data_list = self.get_input("filtered_data_list") or []
        project_code = self.get_input("project_code")
        crawler_task_id = self.get_input("crawler_task_id")
        batch_size = self.get_input("batch_size", 50)
        
        if not filtered_data_list:
            logger.warning("⚠️ 没有数据需要保存")
            self.set_output("saved_kol_ids", [])
            return {"saved_count": 0, "error_count": 0, "saved_kol_ids": []}
        
        if not project_code:
            raise ValueError("project_code is required")
        
        saved_count = 0
        error_count = 0
        saved_kol_ids = []
        
        # 分批处理数据
        for i in range(0, len(filtered_data_list), batch_size):
            batch_data = filtered_data_list[i:i + batch_size]
            
            try:
                batch_result = self._save_batch(batch_data, project_code, crawler_task_id)
                saved_count += batch_result["saved_count"]
                error_count += batch_result["error_count"]
                saved_kol_ids.extend(batch_result["saved_kol_ids"])
                
                logger.info(f"📊 批次 {i//batch_size + 1} 保存完成: 成功 {batch_result['saved_count']}, 失败 {batch_result['error_count']}")
                
            except Exception as e:
                logger.error(f"❌ 批次 {i//batch_size + 1} 保存失败: {str(e)}")
                error_count += len(batch_data)
        
        self.set_output("saved_count", saved_count)
        self.set_output("error_count", error_count)
        self.set_output("saved_kol_ids", saved_kol_ids)
        
        logger.info(f"💾 批量保存完成: 总计 {len(filtered_data_list)} 条，成功 {saved_count} 条，失败 {error_count} 条")
        
        return {
            "saved_count": saved_count,
            "error_count": error_count,
            "saved_kol_ids": saved_kol_ids,
            "total_count": len(filtered_data_list)
        }
    
    def _save_batch(self, batch_data: List[Dict], project_code: str, crawler_task_id: int) -> Dict:
        """保存单个批次的数据"""
        db = next(get_db())
        saved_count = 0
        error_count = 0
        saved_kol_ids = []
        
        try:
            for item in batch_data:
                try:
                    # 转换平台枚举
                    platform_str = item.get("platform", "").lower()
                    platform_enum = PlatformEnum(platform_str)
                    
                    # 创建KOL记录
                    kol = KOL(
                        kol_id=item.get("kol_id", ""),
                        nick_name=item.get("nick_name", ""),
                        platform=platform_enum,
                        project_code=project_code,
                        bio=item.get("bio", ""),
                        followers_count=item.get("followers_count", 0),
                        likes_count=item.get("likes_count", 0),
                        source=item.get("source", ""),
                        crawler_task_id=crawler_task_id
                    )
                    
                    # 设置平台特定字段
                    if platform_str == "tiktok":
                        kol.video_count = item.get("video_count", 0)
                    elif platform_str == "instagram":
                        kol.post_count = item.get("post_count", 0)
                        kol.following_count = item.get("following_count", 0)
                    elif platform_str == "youtube":
                        kol.subscriber_count = item.get("subscriber_count", 0)
                        kol.video_count = item.get("video_count", 0)
                    
                    # 设置其他字段
                    if item.get("engagement_rate"):
                        kol.engagement_rate = item["engagement_rate"]
                    
                    if item.get("avg_views"):
                        kol.avg_views = item["avg_views"]
                    
                    db.add(kol)
                    saved_count += 1
                    saved_kol_ids.append(kol.kol_id)
                    
                except Exception as e:
                    logger.error(f"❌ 保存单条KOL数据失败: {str(e)}, 数据: {item}")
                    error_count += 1
                    continue
            
            # 提交批次
            db.commit()
            
            return {
                "saved_count": saved_count,
                "error_count": error_count,
                "saved_kol_ids": saved_kol_ids
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"❌ 批次提交失败: {str(e)}")
            return {
                "saved_count": 0,
                "error_count": len(batch_data),
                "saved_kol_ids": []
            }
        finally:
            db.close()


class UpdateKOLWithPlatformDataStep(Step):
    """使用平台数据更新KOL信息步骤"""
    
    def run(self):
        """使用平台爬虫数据更新KOL信息"""
        saved_kol_ids = self.get_input("saved_kol_ids") or []
        platform_data_results = self.get_input("platform_data_results") or {}
        
        if not saved_kol_ids:
            logger.warning("⚠️ 没有需要更新的KOL")
            return {"updated_count": 0, "error_count": 0}
        
        if not platform_data_results:
            logger.warning("⚠️ 没有平台数据用于更新")
            return {"updated_count": 0, "error_count": 0}
        
        updated_count = 0
        error_count = 0
        
        db = next(get_db())
        try:
            for kol_id in saved_kol_ids:
                if kol_id not in platform_data_results:
                    continue
                
                platform_data = platform_data_results[kol_id]
                
                if not platform_data.get("success"):
                    error_count += 1
                    continue
                
                try:
                    # 查找KOL记录
                    kol = db.query(KOL).filter(KOL.kol_id == kol_id).first()
                    
                    if not kol:
                        logger.warning(f"⚠️ 未找到KOL记录: {kol_id}")
                        error_count += 1
                        continue
                    
                    # 更新平台数据
                    user_info = platform_data.get("user_info", {})
                    
                    if user_info.get("email"):
                        kol.email = user_info["email"]
                    
                    if user_info.get("bio"):
                        kol.bio = user_info["bio"]
                    
                    if user_info.get("followers_count"):
                        kol.followers_count = user_info["followers_count"]
                    
                    # 更新hashtags和captions
                    videos = platform_data.get("videos", [])
                    if videos:
                        # 提取hashtags
                        all_hashtags = []
                        all_captions = []
                        
                        for video in videos:
                            if video.get("hashtags"):
                                all_hashtags.extend(video["hashtags"])
                            
                            if video.get("desc"):
                                all_captions.append(video["desc"])
                        
                        # 去重并限制数量
                        if all_hashtags:
                            kol.hashtags = list(set(all_hashtags))[:50]
                        
                        if all_captions:
                            kol.captions = all_captions[:20]
                    
                    updated_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ 更新KOL {kol_id} 失败: {str(e)}")
                    error_count += 1
                    continue
            
            db.commit()
            
            self.set_output("updated_count", updated_count)
            self.set_output("error_count", error_count)
            
            logger.info(f"🔄 KOL平台数据更新完成: 成功 {updated_count} 条，失败 {error_count} 条")
            
            return {
                "updated_count": updated_count,
                "error_count": error_count
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"❌ 批量更新KOL数据失败: {str(e)}")
            raise
        finally:
            db.close()
