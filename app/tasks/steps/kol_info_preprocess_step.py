from app.crud import crawler_task
from app.models.enums import CrawlerTaskStatusEnum
from app.tasks.step import Step
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional, Tuple
from app.db.session import get_db
from app.crud.kol import kol
from app.logging_config import get_task_logger

logger = get_task_logger("kol_info_preprocess_step")


class KOLInfoPreprocessStep(Step):
    """KOL信息预处理步骤"""
    def run(self):
        """执行KOL信息预处理"""
        kol_infos = self.get_input("kol_infos")
        self._filter_exists_kols(kol_infos)
        
    def _filter_exists_kols(self, kol_infos: List[Dict[str, Any]]) -> None:
        """
        过滤已存在的KOL，从原始kol_infos列表中剔除不需要处理的KOL

        如果已经在db中爬取过，则跳过不需要重复爬取，根据social_id,platform,project_code， update_at在kols表中去重
        social_id,platform,project_code 用于寻找唯一的kol
        以下两种情况需要爬取：
        1. kol未入库
        2. kol入库了，但update_at已经是两周前的 也需要爬取

        这里需要实现高效的去重逻辑，每次进来的kol_infos有500条左右，只保留需要爬取的kol,并且通过日志记录log_msg到kols表中

        Args:
            kol_infos: KOL信息列表，会被直接修改，移除不需要处理的KOL
        """
        if not kol_infos:
            logger.info("没有KOL信息需要处理")
            return

        original_count = len(kol_infos)
        logger.info(f"开始过滤KOL信息，总数: {original_count}")
        crawler_task_id = self.get_input("crawler_task_id")

        # 提取需要查询的KOL标识信息
        kol_identifiers = self._extract_kol_identifiers(kol_infos)

        # 批量查询数据库中已存在的KOL
        existing_kols_map = self._batch_query_existing_kols_efficient(kol_identifiers)

        # 统计信息
        skipped_count = 0
        need_update_count = 0
        new_kol_count = 0

        # 创建时区感知的时间对象，与数据库中的updated_at字段保持一致
        two_weeks_ago = datetime.now(timezone.utc) - timedelta(weeks=2)

        # 从后往前遍历，这样删除元素时不会影响索引
        for i in range(len(kol_infos) - 1, -1, -1):
            kol_info = kol_infos[i]

            # 构建KOL唯一标识
            identifier = self._build_kol_identifier(kol_info)
            if not identifier:
                logger.warning(f"KOL信息缺少必要字段，从列表中移除: {kol_info}")
                kol_infos.pop(i)
                continue

            existing_kol = existing_kols_map.get(identifier)

            if existing_kol is None:
                # 情况1: KOL未入库，需要爬取，保留在列表中
                new_kol_count += 1
                logger.debug(f"新KOL需要爬取: {identifier}")
            elif self._is_datetime_older_than(existing_kol.updated_at, two_weeks_ago):
                # 情况2: KOL入库了，但update_at已经是两周前的，需要爬取，保留在列表中
                need_update_count += 1
                logger.debug(f"KOL数据过期需要更新: {identifier}, 上次更新: {existing_kol.updated_at}")
            else:
                # KOL数据较新，跳过，从列表中移除
                skipped_count += 1
                logger.debug(f"KOL数据较新，从列表中移除: {identifier}, 上次更新: {existing_kol.updated_at}")
                kol_infos.pop(i)

        # 记录统计信息
        log_msg = (f"KOL过滤完成 - 原始数量: {original_count}, "
                  f"需要爬取: {len(kol_infos)} "
                  f"(新KOL: {new_kol_count}, 需更新: {need_update_count}), "
                  f"跳过: {skipped_count}")
        db = next(get_db())
        logger.info(log_msg)
        crawler_task.update_status(db, task_id=crawler_task_id, status=CrawlerTaskStatusEnum.RUNNING, log_msg=log_msg)
        self.set_output("kol_infos", kol_infos)
    def _extract_kol_identifiers(self, kol_infos: List[Dict[str, Any]]) -> List[Tuple[str, str, str]]:
        """
        从KOL信息列表中提取唯一标识符

        Returns:
            List[Tuple[str, str, str]]: (social_id, platform, project_code) 的列表
        """
        identifiers = []
        for kol_info in kol_infos:
            identifier = self._build_kol_identifier(kol_info)
            if identifier is not None:
                identifiers.append(identifier)
        return identifiers

    def _build_kol_identifier(self, kol_info: Dict[str, Any]) -> Optional[Tuple[str, str, str]]:
        """
        构建KOL唯一标识符

        Args:
            kol_info: KOL信息字典

        Returns:
            Optional[Tuple[str, str, str]]: (social_id, platform, project_code) 或 None
        """
        social_id = kol_info.get("social_id")
        platform = str(kol_info.get("platform")).upper()
        project_code = kol_info.get("project_code")

        # 检查必要字段
        if not all([social_id, platform, project_code]):
            return None

        return (str(social_id), platform, str(project_code))

    def _batch_query_existing_kols_efficient(self, identifiers: List[Tuple[str, str, str]]) -> Dict[Tuple[str, str, str], Any]:
        """
        高效批量查询数据库中已存在的KOL，使用IN查询替代多次单独查询

        Args:
            identifiers: KOL标识符列表

        Returns:
            Dict: 以标识符为key，KOL对象为value的字典
        """
        if not identifiers:
            return {}

        try:
            # 获取数据库会话
            db_gen = get_db()
            db = next(db_gen)

            try:
                # 使用新的高效批量查询方法
                existing_kols_map = kol.get_multi_by_filter(
                    db=db,
                    filter_tuples=identifiers,
                    filter_columns=["social_id", "platform", "project_code"]
                )
                logger.info(f"高效批量查询完成，找到 {len(existing_kols_map)} 个已存在的KOL")
                return existing_kols_map

            finally:
                # 确保关闭数据库会话
                db.close()

        except Exception as e:
            logger.error(f"高效批量查询KOL失败: {e}")
            return {}

    def _is_datetime_older_than(self, db_datetime, threshold_datetime):
        """
        安全地比较两个datetime对象，处理时区感知和时区无关的情况

        Args:
            db_datetime: 数据库中的datetime对象
            threshold_datetime: 阈值datetime对象

        Returns:
            bool: 如果db_datetime早于threshold_datetime返回True
        """
        try:
            # 如果db_datetime是时区无关的，将其转换为UTC时区
            if db_datetime.tzinfo is None:
                db_datetime = db_datetime.replace(tzinfo=timezone.utc)

            # 如果threshold_datetime是时区无关的，将其转换为UTC时区
            if threshold_datetime.tzinfo is None:
                threshold_datetime = threshold_datetime.replace(tzinfo=timezone.utc)

            return db_datetime < threshold_datetime

        except Exception as e:
            logger.warning(f"时间比较失败，默认认为需要更新: {e}")
            # 如果比较失败，默认认为需要更新（保守策略）
            return True
