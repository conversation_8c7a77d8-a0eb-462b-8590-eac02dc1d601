"""
Gmail数据同步步骤
将Gmail邮件数据同步到candidates表
"""
from app.tasks.step import Step
from app.services.gmail_sync_services import handler
from app.logging_config import get_task_logger

logger = get_task_logger("sync_gmail_step")


class SyncGmailStep(Step):
    """Gmail数据同步步骤"""

    def run(self):
        """执行Gmail数据同步"""
        # 获取输入参数
        project_code = self.get_input("project_code")
        if not project_code:
            raise ValueError("project_code is required")

        logger.info(f"开始同步Gmail数据，项目代码: {project_code}")

        try:
            # 直接调用gmail_sync_services中的handler方法
            result = handler(project_code)

            logger.info(f"Gmail数据同步完成，项目: {project_code}, 结果: {result}")

            # 设置输出结果
            self.set_output("sync_result", {
                "success": True,
                "result": result,
                "message": f"Gmail同步完成，项目: {project_code}"
            })

            return result

        except Exception as e:
            logger.error(f"Gmail数据同步失败: {e}")
            self.set_output("sync_result", {"success": False, "error": str(e)})
            raise