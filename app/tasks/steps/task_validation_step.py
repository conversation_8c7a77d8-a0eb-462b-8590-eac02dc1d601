"""
任务参数验证步骤
验证爬虫任务的输入参数
"""
from typing import Dict, Any, Optional
from app.tasks.step import Step
from app.models.enums import PlatformEnum, SourceEnum
from app.logging_config import get_task_logger
from pydantic import BaseModel

logger = get_task_logger("task_validation_step")

class ParamsChecker(BaseModel):
    task_name: str
    source: SourceEnum
    platform: PlatformEnum
    project_code: str
    filters: dict
    cookies: str
    class Config:
        str_min_length = 1
        str_strip_whitespace = True

class TaskParameterValidationStep(Step):
    """任务参数验证步骤"""
    
    def run(self):
        """验证任务参数"""
        input_data = self.get_input("input_data")
        ParamsChecker(**input_data)
        self.set_output("validated_params", input_data)
