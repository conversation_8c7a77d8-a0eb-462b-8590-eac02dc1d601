"""
KOL邮箱获取步骤
通过三个步骤获取KOL邮箱：
1. 解析bio信息提取邮箱
2. 通过大模型评分
3. 通过nano接口获取邮箱
"""
from typing import Optional

from app.tasks.step import Step
from app.db.session import get_db
from app.crud import kol
from app.services.llm_service import email_and_keywords_from_bio
from app.logging_config import get_task_logger

logger = get_task_logger("fetch_kol_email_step")


class FetchKOLEmailStep(Step):
    """KOL邮箱获取步骤"""

    def run(self):
        """执行KOL邮箱获取流程"""
        # 获取输入参数
        kol_id = self.get_input("kol_id")
        if not kol_id:
            raise ValueError("kol_id is required")

        db = next(get_db())

        try:
            # 获取KOL信息
            kol_obj = kol.get_by_kol_id(db, kol_id=kol_id)
            if not kol_obj:
                raise ValueError(f"KOL with id {kol_id} not found")

            logger.info(f"开始为KOL {kol_id} ({kol_obj.nick_name}) 获取邮箱")

            # 如果已经有邮箱，跳过处理
            if kol_obj.email:
                logger.info(f"KOL {kol_id} 已有邮箱: {kol_obj.email}")
                self.set_output("email_found", True)
                self.set_output("email", kol_obj.email)
                self.set_output("source", "existing")
                return {"status": "completed", "email": kol_obj.email, "source": "existing", "ai_matched": kol_obj.ai_matched}

            # 只进行bio解析
            email_from_bio = self._extract_email_from_bio(db, kol_obj)
            if email_from_bio:
                logger.info(f"从bio中提取到邮箱: {email_from_bio}")
                self.set_output("email_found", True)
                self.set_output("email", email_from_bio)
                self.set_output("source", "bio")
                return {"status": "completed", "email": email_from_bio, "source": "bio"}

            # bio解析完成，但未找到邮箱
            logger.info(f"KOL {kol_id} bio解析完成，未找到邮箱")
            self.set_output("email_found", False)
            return {"status": "completed", "email": None, "source": "bio_parsed"}

        finally:
            db.close()

    def _extract_email_from_bio(self, db, kol_obj) -> Optional[str]:
        """步骤1: 从bio中提取邮箱"""
        # 检查是否已经解析过bio
        if kol_obj.bio_parsed_at:
            logger.info(f"KOL {kol_obj.id} bio已解析过，跳过")
            return kol_obj.bio_extracted_email

        if not kol_obj.bio:
            logger.info(f"KOL {kol_obj.id} 没有bio信息")
            # 标记bio已解析（虽然没有内容）
            kol.update_bio_parsed_status(db, kol_id=kol_obj.id)
            return None

        try:
            logger.info(f"开始解析KOL {kol_obj.id} 的bio信息")
            # 调用LLM服务提取邮箱
            result = email_and_keywords_from_bio(kol_obj.bio)

            extracted_email = None
            if result and isinstance(result, dict):
                extracted_email = result.get("email")
            elif isinstance(result, str) and "@" in result:
                # 如果直接返回邮箱字符串
                extracted_email = result

            # 更新数据库状态
            kol.update_bio_parsed_status(
                db,
                kol_id=kol_obj.id,
                extracted_email=extracted_email
            )

            logger.info(f"bio解析完成，提取邮箱: {extracted_email}")
            return extracted_email

        except Exception as e:
            logger.error(f"bio解析失败: {str(e)}")
            # 即使失败也要标记已解析，避免重复调用
            kol.update_bio_parsed_status(db, kol_id=kol_obj.id)
            return None



