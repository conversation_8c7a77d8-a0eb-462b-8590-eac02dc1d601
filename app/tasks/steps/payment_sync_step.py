"""
支付数据同步步骤
管理支付记录的创建和同步
"""
from typing import List, Dict, Any, Optional
from decimal import Decimal
from datetime import datetime
from sqlalchemy.orm import Session
from app.tasks.step import Step
from app.models.payment import Payment
from app.models.performance import Performance
from app.db.session import get_db
from app.logging_config import get_task_logger

logger = get_task_logger("payment_sync_step")


class GetPendingPaymentsStep(Step):
    """获取待处理支付记录步骤"""
    
    def run(self):
        """获取需要同步到绩效表的支付记录"""
        project_code = self.get_input("project_code")
        
        # 这里模拟从外部系统获取支付数据
        # 实际使用时可能从CSV文件、API或其他数据源获取
        pending_payments = self.get_input("pending_payments") or []
        
        if not pending_payments:
            logger.info("没有待处理的支付记录")
            self.set_output("pending_payments", [])
            return {"pending_payments": [], "count": 0}
        
        # 验证和标准化支付数据
        validated_payments = []
        for payment in pending_payments:
            try:
                validated_payment = {
                    "kol_id": payment.get("kol_id", "").strip(),
                    "platform": payment.get("platform", "").strip().lower(),
                    "project_code": payment.get("project_code", project_code).strip(),
                    "post_link": payment.get("post_link", "").strip(),
                    "payment_amount": self._parse_decimal(payment.get("payment_amount")),
                    "paypal_accounts": payment.get("paypal_accounts", "").strip(),
                    "tracker": payment.get("tracker", "").strip(),
                    "fund_source": payment.get("fund_source", "").strip(),
                    "note": payment.get("note", "").strip()
                }
                
                # 验证必需字段
                if not all([validated_payment["kol_id"], validated_payment["platform"], 
                           validated_payment["project_code"], validated_payment["post_link"]]):
                    logger.warning(f"跳过无效支付记录: {payment}")
                    continue
                
                validated_payments.append(validated_payment)
                
            except Exception as e:
                logger.error(f"验证支付记录失败: {payment}, 错误: {str(e)}")
                continue
        
        self.set_output("pending_payments", validated_payments)
        
        logger.info(f"获取待处理支付记录: {len(validated_payments)} 条")
        
        return {
            "pending_payments": validated_payments,
            "count": len(validated_payments)
        }
    
    def _parse_decimal(self, value) -> Optional[Decimal]:
        """解析金额为Decimal类型"""
        if not value:
            return None
        
        try:
            if isinstance(value, str):
                # 移除货币符号和空格
                value = value.replace("$", "").replace("¥", "").replace(",", "").strip()
            
            return Decimal(str(value))
        except Exception:
            return None


class SyncPaymentToPerformanceStep(Step):
    """同步支付记录到绩效表步骤"""
    
    def run(self):
        """将支付记录同步到绩效表"""
        payment_data = self.get_input("payment_data")
        
        if not payment_data:
            raise ValueError("payment_data is required")
        
        db = next(get_db())
        try:
            # 检查是否已存在对应的绩效记录
            existing_performance = db.query(Performance).filter(
                Performance.post_link == payment_data["post_link"]
            ).first()
            
            if existing_performance:
                logger.info(f"绩效记录已存在: {payment_data['post_link']}")
                performance_id = existing_performance.id
            else:
                # 创建新的绩效记录
                from app.tasks.steps.performance_management_step import CreatePerformanceRecordStep
                
                create_step = CreatePerformanceRecordStep(self.shared_data)
                create_step.shared_data.update({
                    "kol_id": payment_data["kol_id"],
                    "platform": payment_data["platform"],
                    "project_code": payment_data["project_code"],
                    "post_link": payment_data["post_link"],
                    "payment_amount": payment_data["payment_amount"]
                })
                
                result = create_step.run()
                performance_id = result["performance_id"]
                
                logger.info(f"创建绩效记录: {performance_id}")
            
            # 检查是否已存在支付记录
            existing_payment = db.query(Payment).filter(
                Payment.performance_id == performance_id
            ).first()
            
            if existing_payment:
                # 更新现有支付记录
                if payment_data.get("payment_amount"):
                    existing_payment.payment_amount = payment_data["payment_amount"]
                if payment_data.get("paypal_accounts"):
                    existing_payment.paypal_accounts = payment_data["paypal_accounts"]
                if payment_data.get("tracker"):
                    existing_payment.tracker = payment_data["tracker"]
                if payment_data.get("fund_source"):
                    existing_payment.fund_source = payment_data["fund_source"]
                if payment_data.get("note"):
                    existing_payment.note = payment_data["note"]
                
                db.commit()
                
                logger.info(f"更新支付记录: {existing_payment.id}")
                
                return {
                    "action": "updated",
                    "payment_id": existing_payment.id,
                    "performance_id": performance_id
                }
            else:
                # 创建新的支付记录
                payment = Payment(
                    performance_id=performance_id,
                    payment_amount=payment_data.get("payment_amount"),
                    paypal_accounts=payment_data.get("paypal_accounts"),
                    tracker=payment_data.get("tracker"),
                    fund_source=payment_data.get("fund_source"),
                    note=payment_data.get("note")
                )
                
                db.add(payment)
                db.commit()
                db.refresh(payment)
                
                logger.info(f"创建支付记录: {payment.id}")
                
                return {
                    "action": "created",
                    "payment_id": payment.id,
                    "performance_id": performance_id
                }
            
        except Exception as e:
            db.rollback()
            logger.error(f"同步支付记录失败: {str(e)}")
            raise
        finally:
            db.close()


class BatchSyncPaymentsStep(Step):
    """批量同步支付记录步骤"""
    
    def run(self):
        """批量同步多个支付记录"""
        pending_payments = self.get_input("pending_payments") or []
        
        if not pending_payments:
            logger.warning("没有支付记录需要同步")
            return {"synced_count": 0, "failed_count": 0}
        
        synced_count = 0
        failed_count = 0
        
        for payment_data in pending_payments:
            try:
                sync_step = SyncPaymentToPerformanceStep(self.shared_data)
                sync_step.shared_data["payment_data"] = payment_data
                
                result = sync_step.run()
                
                synced_count += 1
                logger.info(f"同步支付记录成功: {payment_data['post_link']} -> {result['action']}")
                
            except Exception as e:
                failed_count += 1
                logger.error(f"同步支付记录失败: {payment_data['post_link']}, 错误: {str(e)}")
        
        self.set_output("synced_count", synced_count)
        self.set_output("failed_count", failed_count)
        
        logger.info(f"批量同步完成: 成功 {synced_count} 条，失败 {failed_count} 条")
        
        return {
            "synced_count": synced_count,
            "failed_count": failed_count,
            "total_count": len(pending_payments)
        }


class UpdatePaymentStatusStep(Step):
    """更新支付状态步骤"""
    
    def run(self):
        """更新支付记录的状态信息"""
        payment_id = self.get_input("payment_id")
        payout_date = self.get_input("payout_date")
        payment_screenshot_filename = self.get_input("payment_screenshot_filename")
        
        if not payment_id:
            raise ValueError("payment_id is required")
        
        db = next(get_db())
        try:
            payment = db.query(Payment).filter(Payment.id == payment_id).first()
            
            if not payment:
                raise ValueError(f"Payment record not found: {payment_id}")
            
            # 更新支付日期
            if payout_date:
                if isinstance(payout_date, str):
                    payment.payout_date = datetime.fromisoformat(payout_date)
                else:
                    payment.payout_date = payout_date
            
            # 更新支付截图文件名
            if payment_screenshot_filename:
                payment.payment_screenshot_filename = payment_screenshot_filename
            
            db.commit()
            db.refresh(payment)
            
            logger.info(f"更新支付状态成功: payment_id={payment_id}")
            
            return {
                "payment_id": payment_id,
                "payout_date": payment.payout_date,
                "payment_screenshot_filename": payment.payment_screenshot_filename
            }
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新支付状态失败: {str(e)}")
            raise
        finally:
            db.close()


class GetPaymentSummaryStep(Step):
    """获取支付汇总信息步骤"""
    
    def run(self):
        """获取支付记录的汇总统计"""
        project_code = self.get_input("project_code")
        
        db = next(get_db())
        try:
            query = db.query(Payment).join(Performance)
            
            if project_code:
                query = query.filter(Performance.project_code == project_code)
            
            payments = query.all()
            
            # 计算汇总信息
            total_payments = len(payments)
            total_amount = sum(p.payment_amount or 0 for p in payments)
            paid_count = sum(1 for p in payments if p.payout_date)
            pending_count = total_payments - paid_count
            
            summary = {
                "total_payments": total_payments,
                "total_amount": float(total_amount),
                "paid_count": paid_count,
                "pending_count": pending_count,
                "paid_percentage": (paid_count / total_payments * 100) if total_payments > 0 else 0
            }
            
            self.set_output("payment_summary", summary)
            
            logger.info(f"支付汇总: {summary}")
            
            return summary
            
        except Exception as e:
            logger.error(f"获取支付汇总失败: {str(e)}")
            raise
        finally:
            db.close()
