"""
绩效数据同步步骤
封装 TikHub API 绩效数据获取方法
"""
import asyncio
from typing import Dict, Any, Optional, List, Union
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from app.tasks.step import Step
from app.models.performance import Performance
from app.models.enums import PlatformEnum
from app.db.session import get_db, get_async_db
from app.crud.performance import performance
from app.logging_config import get_task_logger
from app.services.sync_performance_services import (
    get_tk_pf_data, get_ins_pf_data, get_yt_pf_data, get_performance_data_batch
)

logger = get_task_logger("sync_performance_step")


def _save_performance_data(performance_data: Dict[str, Any], performance_id: Optional[int] = None) -> Dict[str, Any]:
    """保存绩效数据到数据库的通用方法"""
    db = next(get_db())
    try:
        if performance_id:
            # 更新现有记录
            performance = db.query(Performance).filter(Performance.id == performance_id).first()
            if not performance:
                raise ValueError(f"Performance record not found: {performance_id}")
        else:
            # 检查是否已存在相同的post_link
            existing = db.query(Performance).filter(
                Performance.post_link == performance_data["post_link"]
            ).first()

            if existing:
                performance = existing
                logger.info(f"更新现有绩效记录: {performance_data['post_link']}")
            else:
                # 创建新记录
                platform_enum = PlatformEnum(performance_data["platform"])
                performance = Performance(
                    kol_id=performance_data["kol_id"],
                    platform=platform_enum,
                    project_code=performance_data["project_code"],
                    post_link=performance_data["post_link"]
                )
                db.add(performance)
                db.flush()  # 获取ID
                logger.info(f"创建新绩效记录: {performance_data['post_link']}")

        # 更新绩效数据
        performance.post_date = performance_data.get("post_date")
        performance.views_total = performance_data.get("views_total", 0)
        performance.likes_total = performance_data.get("likes_total", 0)
        performance.comments_total = performance_data.get("comments_total", 0)
        performance.shares_total = performance_data.get("shares_total", 0)

        # 更新天数数据
        if performance_data.get("views_day1") is not None:
            performance.views_day1 = performance_data["views_day1"]
            performance.likes_day1 = performance_data["likes_day1"]
            performance.comments_day1 = performance_data["comments_day1"]
            performance.shares_day1 = performance_data["shares_day1"]

        if performance_data.get("views_day3") is not None:
            performance.views_day3 = performance_data["views_day3"]
            performance.likes_day3 = performance_data["likes_day3"]
            performance.comments_day3 = performance_data["comments_day3"]
            performance.shares_day3 = performance_data["shares_day3"]

        if performance_data.get("views_day7") is not None:
            performance.views_day7 = performance_data["views_day7"]
            performance.likes_day7 = performance_data["likes_day7"]
            performance.comments_day7 = performance_data["comments_day7"]
            performance.shares_day7 = performance_data["shares_day7"]

        db.commit()
        db.refresh(performance)

        return {
            "performance_id": performance.id,
            "action": "updated" if performance_id or existing else "created"
        }

    except Exception as e:
        db.rollback()
        logger.error(f"保存绩效数据失败: {str(e)}")
        raise
    finally:
        db.close()


class BatchSyncPerformanceStep(Step):
    """批量同步绩效数据步骤 - 使用异步并发和批量upsert"""

    def run(self):
        """批量同步多个平台的绩效数据"""
        post_links_data = self.get_input("post_links_data") or []
        max_concurrent = self.get_input("max_concurrent") or 5

        if not post_links_data:
            logger.warning("没有绩效记录需要同步")
            return {
                "total_count": 0,
                "success_count": 0,
                "failed_count": 0
            }

        # 使用同步方法处理
        result = self._sync_batch_sync(post_links_data, max_concurrent)

        self.set_output("sync_results", result)

        logger.info(f"批量同步完成: 总计 {result['total_count']} 条，成功 {result['success_count']} 条，失败 {result['failed_count']} 条")

        return result

    def _sync_batch_sync(self, post_links_data: List, max_concurrent: int) -> Dict:
        """同步批量同步绩效数据"""
        logger.info(f"开始批量同步 {len(post_links_data)} 条绩效数据，并发数: {max_concurrent}")

        try:

            # 1. 使用 get_performance_data_batch 并发获取绩效数据
            performance_data_list = get_performance_data_batch(post_links_data, max_concurrent)

            if not performance_data_list:
                logger.warning("没有成功获取到绩效数据")
                return {
                    "total_count": len(post_links_data),
                    "success_count": 0,
                    "failed_count": len(post_links_data)
                }

            # 2. 异步批量保存到数据库
            save_result = self._async_batch_save_performance_data(performance_data_list)

            return {
                "total_count": len(post_links_data),
                "success_count": save_result.get("success_count", 0),
                "failed_count": save_result.get("failed_count", 0),
                "performance_data_count": len(performance_data_list),
                "save_error": save_result.get("error")
            }

        except Exception as e:
            logger.error(f"批量同步失败: {str(e)}")
            return {
                "total_count": len(post_links_data),
                "success_count": 0,
                "failed_count": len(post_links_data),
                "error": str(e)
            }

    def _async_batch_save_performance_data(self, performance_data_list: List[Dict]) -> Dict:
        """异步批量保存绩效数据到数据库"""
        if not performance_data_list:
            return {"success_count": 0, "failed_count": 0}

        # 使用异步方法处理
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(
            self._async_save_to_database(performance_data_list)
        )

    async def _async_save_to_database(self, performance_data_list: List[Dict]) -> Dict:
        """异步保存数据到数据库的核心方法"""
        logger.info(f"开始异步批量保存 {len(performance_data_list)} 条绩效数据")

        batch_size = 500  # 每批500条
        success_count = 0
        failed_count = 0

        async for db in get_async_db():
            try:
                # 分批处理数据
                for i in range(0, len(performance_data_list), batch_size):
                    batch = performance_data_list[i:i + batch_size]

                    try:
                        # 使用 async_batch_upsert 方法
                        conflict_columns = ["post_link"]  # 使用 post_link 作为冲突检测列

                        # 使用 async_batch_upsert 方法
                        upserted_records = await performance.async_batch_upsert(
                            db=db,
                            objs_in=batch,  # type: ignore
                            conflict_columns=conflict_columns,
                            batch_size=batch_size
                        )

                        batch_success = len(upserted_records)
                        batch_failed = len(batch) - batch_success

                        success_count += batch_success
                        failed_count += batch_failed

                        logger.info(f"批次 {i//batch_size + 1} 完成: 成功 {batch_success} 条，失败 {batch_failed} 条")

                    except Exception as e:
                        logger.error(f"批次 {i//batch_size + 1} 保存失败: {str(e)}")
                        failed_count += len(batch)

                logger.info(f"异步批量保存完成: 总成功 {success_count} 条，总失败 {failed_count} 条")

                return {
                    "success_count": success_count,
                    "failed_count": failed_count
                }

            except Exception as e:
                logger.error(f"异步批量保存失败: {str(e)}")
                return {
                    "success_count": 0,
                    "failed_count": len(performance_data_list),
                    "error": str(e)
                }

    def _sync_batch_save_performance_data(self, performance_data_list: List[Dict]) -> Dict:
        """同步批量保存绩效数据到数据库（备用方法）"""
        if not performance_data_list:
            return {"success_count": 0, "failed_count": 0}

        logger.info(f"开始同步批量保存 {len(performance_data_list)} 条绩效数据")

        db = next(get_db())
        try:
            success_count = 0
            failed_count = 0

            # 分批处理数据
            batch_size = 100
            for i in range(0, len(performance_data_list), batch_size):
                batch = performance_data_list[i:i + batch_size]

                for data in batch:
                    try:
                        # 检查是否已存在记录
                        existing = db.query(Performance).filter(
                            Performance.post_link == data["post_link"]
                        ).first()

                        if existing:
                            # 更新现有记录
                            for key, value in data.items():
                                if hasattr(existing, key) and value is not None:
                                    setattr(existing, key, value)
                        else:
                            # 创建新记录
                            new_record = Performance(**data)
                            db.add(new_record)

                        success_count += 1

                    except Exception as e:
                        logger.error(f"处理单条记录失败: {data.get('post_link', 'unknown')}, 错误: {str(e)}")
                        failed_count += 1

                # 提交批次
                db.commit()
                logger.info(f"批次 {i//batch_size + 1} 完成: 成功处理 {len(batch)} 条")

            logger.info(f"同步批量保存完成: 成功 {success_count} 条，失败 {failed_count} 条")

            return {
                "success_count": success_count,
                "failed_count": failed_count
            }

        except Exception as e:
            db.rollback()
            logger.error(f"同步批量保存失败: {str(e)}")
            return {
                "success_count": 0,
                "failed_count": len(performance_data_list),
                "error": str(e)
            }
        finally:
            db.close()
