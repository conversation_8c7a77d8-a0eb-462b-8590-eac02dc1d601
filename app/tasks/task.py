
"""
爬虫任务实现
整合各种爬虫步骤，实现完整的数据采集流程
"""
from app.tasks.step import Step
from app.tasks.steps.fetch_kol_email_step import FetchKOLEmailStep
from app.tasks.steps.kol_info_preprocess_step import KOLInfoPreprocessStep
from app.tasks.steps.kol_info_step import K<PERSON>InfoStep
from app.utils.wrapper import log_run_time
from app.logging_config import get_task_logger


logger = get_task_logger("crawler_task")


class Task:
    """任务基类"""

    def __init__(self, shared_data={}):
        self.steps = []
        self.shared_data = shared_data

    @log_run_time
    def run(self) -> dict:
        results = {}
        for step_class in self.steps:
            step_instance = step_class(self.shared_data)
            result = step_instance.run_with_log()
            results[step_class.__name__] = result
            self.shared_data.update(step_instance.shared_data)
        return results



class ModashCrawlerTask(Task):
    """数据采集器V3任务 - 完整的KOL数据采集流程"""

    def __init__(self, shared_data=None):
        super().__init__(shared_data)
        # 导入所有需要的步骤
        from app.tasks.steps.modash_search_step import ModashSearchStep
        # 配置完整的步骤流程
        self.steps = [
            ModashSearchStep,                      # 1. 数据采集（Modash搜索）
            KOLInfoPreprocessStep,                 # 2. KOL信息预处理
            KOLInfoStep,                           # 3. 入库
        ]
        self.step_params = {}


class SyncGmailTask(Task):
    """将Gmail数据同步到candidates表"""

    def __init__(self, shared_data=None):
        super().__init__(shared_data)
        # 导入SyncGmailStep
        from app.tasks.steps.sync_gmail_step import SyncGmailStep

        self.steps = [
            SyncGmailStep,
        ]


class PerformanceSyncTask(Task):
    """绩效数据同步任务 - 批量同步所有平台的绩效数据"""

    def __init__(self, shared_data=None):
        super().__init__(shared_data)
        # 导入绩效同步步骤
        from app.tasks.steps.sync_performance_step import BatchSyncPerformanceStep
        from app.tasks.steps.performance_management_step import GetPerformanceRecordsStep

        # 只保留批量同步流程：先获取记录，再批量同步
        self.steps = [
            GetPerformanceRecordsStep,
            BatchSyncPerformanceStep
        ]