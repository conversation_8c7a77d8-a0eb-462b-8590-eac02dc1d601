"""
邮件发送服务 - 简化版本，只保留 Postmark 邮件发送器
"""
import json
import requests
from typing import List, Dict
from app.logging_config import get_task_logger

logger = get_task_logger("email_services")


class PostmarkEmailSender:
    """Postmark邮件发送器"""

    def __init__(self, server_token: str):
        """
        初始化Postmark发送器
        :param server_token: Postmark Server Token
        """
        self.server_token = server_token
        self.base_url = "https://api.postmarkapp.com"
        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "X-Postmark-Server-Token": server_token
        }

    def batch_send_template_emails(
        self,
        from_email: str,
        template_code: str,
        recipients: List[Dict[str, str]],
        message_stream: str = "broadcast",
        batch_size: int = 400
    ) -> List[Dict]:
        """
        批量发送模板邮件

        Args:
        批量发送模板邮件
        :param from_email: 发件人邮箱
        :param template_id: 模板ID
        :param recipients: 收件人列表，格式: [{"email": "<EMAIL>", "name": "<PERSON>", "account_link": "link"}]
        :param message_stream: 消息流，默认为"broadcast"用于营销邮件
        :param batch_size: 每批发送的邮件数量(最大400)
        :return: 发送结果列表

        Returns:
            发送结果列表
        """
        results = []
        
        # 分批处理
        for i in range(0, len(recipients), batch_size):
            batch = recipients[i:i + batch_size]
            
            # 构建请求数据
            messages = []
            for recipient in batch:
                message = {
                    "From": from_email,
                    "To": recipient["email"],
                    "TemplateId": template_code,
                    "TemplateModel": {
                        "name": recipient.get("name", ""),
                        "account_link": recipient.get("account_link", "")
                    },
                    "MessageStream": message_stream,
                    "TrackOpens": True,
                    "TrackLinks": "HtmlAndText"
                }
                messages.append(message)
            
            # 发送请求
            payload = {"Messages": messages}
            try:
                response = requests.post(
                    f"{self.base_url}/email/batchWithTemplates",
                    headers=self.headers,
                    json = payload,
                    timeout=30
                )
                response.raise_for_status()
                results.extend(response.json())
            except requests.exceptions.RequestException as e:
                logger.error(f"Postmark API请求失败: {str(e)}")
                error_result = {"success": False, "error": str(e)}
                results.extend([error_result] * len(batch))

        return results


# 移除 EmailService 类和相关便捷函数
