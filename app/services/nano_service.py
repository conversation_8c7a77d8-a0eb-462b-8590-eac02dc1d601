"""
Nano API服务 - 用于获取KOL邮箱数据并回填到主表
"""
import requests
import aiohttp
import asyncio
from typing import Optional, Dict, Any, List
from urllib.parse import quote, quote_plus
from sqlalchemy.orm import Session

from app.crud.kol import kol as kol_crud
from app.logging_config import get_task_logger

logger = get_task_logger("nano_service")


class NanoService:
    """Nano API服务类"""

    def __init__(self):
        self.base_url = "https://public-api.similartube.co/get_data_by_url"
        self.bearer_token = "mgpsw1v4lz23iwio7vjsqd7o"
        self.headers = {
            "Authorization": f"Bearer {self.bearer_token}"
        }

    def _clean_email_value(self, email_value) -> Optional[str]:
        """清理邮箱值，处理各种无效情况"""
        if email_value is None:
            return None

        # 转换为字符串并去除首尾空格
        email_str = str(email_value).strip()

        # 检查是否为各种形式的null值
        if email_str.lower() in ['null', 'none', 'undefined', 'n/a', 'na']:
            return None

        # 检查是否为空字符串
        if not email_str:
            return None

        # 简单的邮箱格式验证
        if '@' not in email_str or '.' not in email_str:
            return None

        return email_str
    
    def get_kol_email_by_url(self, url: str) -> Dict[str, Any]:
        """
        通过URL获取KOL邮箱数据
        
        Args:
            url: KOL的社交媒体URL
            
        Returns:
            包含邮箱信息的结果字典
        """
        try:
            request_url = f"{self.base_url}?url={url}&contact=1"
            
            logger.info(f"调用Nano API: {request_url}")
            
            # 发送请求
            response = requests.get(request_url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get("code") != 200:
                logger.error(f"Nano API返回错误: {data.get('message', 'Unknown error')}")
                return {
                    "success": False,
                    "message": f"API错误: {data.get('message', 'Unknown error')}",
                    "email": None,
                    "quota": data.get("quota"),
                    "usage": data.get("usage"),
                    "cost": data.get("cost")
                }
            
            # 提取邮箱信息
            kol_data = data.get("data", {})
            email_list = kol_data.get("email", [])
            
            # 处理邮箱数据
            extracted_emails = []
            for email_item in email_list:
                if isinstance(email_item, dict):
                    email_value = email_item.get("value", "").strip()
                    email_type = email_item.get("type", "")
                    
                    if email_value and email_value != "":
                        extracted_emails.append({
                            "email": email_value,
                            "type": email_type
                        })
                elif isinstance(email_item, str) and email_item.strip():
                    extracted_emails.append({
                        "email": email_item.strip(),
                        "type": "direct"
                    })
            
            logger.info(f"成功获取KOL数据，邮箱数量: {len(extracted_emails)}")
            
            # 提取topics信息
            topics = kol_data.get("topics", [])

            return {
                "success": True,
                "message": "获取成功",
                "email": self._clean_email_value(extracted_emails[0]["email"]) if extracted_emails else None,
                "all_emails": extracted_emails,
                "topics": topics,
                "kol_info": {
                    "platform": kol_data.get("platform"),
                    "username": kol_data.get("username"),
                    "name": kol_data.get("name"),
                    "subsCount": kol_data.get("subsCount"),
                    "country": kol_data.get("country")
                },
                "quota": data.get("quota"),
                "usage": data.get("usage"),
                "cost": data.get("cost")
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Nano API请求失败: {str(e)}")
            return {
                "success": False,
                "message": f"请求失败: {str(e)}",
                "email": None
            }
        except Exception as e:
            logger.error(f"处理Nano API响应时发生异常: {str(e)}")
            return {
                "success": False,
                "message": f"处理异常: {str(e)}",
                "email": None
            }

    async def async_get_kol_email_by_url(self, url: str) -> Dict[str, Any]:
        """
        通过URL异步获取KOL邮箱数据

        Args:
            url: KOL的社交媒体URL

        Returns:
            包含邮箱信息的结果字典
        """
        try:
            request_url = f"{self.base_url}?url={url}&contact=1"

            logger.info(f"异步调用Nano API: {request_url}")

            # 创建异步HTTP会话
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # 发送异步请求
                async with session.get(request_url, headers=self.headers) as response:
                    response.raise_for_status()
                    data = await response.json()

            if data.get("code") != 200:
                logger.error(f"Nano API返回错误: {data.get('message', 'Unknown error')}")
                return {
                    "success": False,
                    "message": f"API错误: {data.get('message', 'Unknown error')}",
                    "email": None,
                    "quota": data.get("quota"),
                    "usage": data.get("usage"),
                    "cost": data.get("cost")
                }

            # 提取邮箱信息
            kol_data = data.get("data", {})
            email_list = kol_data.get("email", [])

            # 处理邮箱数据
            extracted_emails = []
            for email_item in email_list:
                if isinstance(email_item, dict):
                    email_value = email_item.get("value", "").strip()
                    email_type = email_item.get("type", "")

                    if email_value and email_value != "":
                        extracted_emails.append({
                            "email": email_value,
                            "type": email_type
                        })
                elif isinstance(email_item, str) and email_item.strip():
                    extracted_emails.append({
                        "email": email_item.strip(),
                        "type": "direct"
                    })

            logger.info(f"异步成功获取KOL数据，邮箱数量: {len(extracted_emails)}")

            # 提取topics信息
            topics = kol_data.get("topics", [])

            return {
                "success": True,
                "message": "获取成功",
                "email": extracted_emails[0]["email"] if extracted_emails else None,
                "all_emails": extracted_emails,
                "topics": topics,
                "kol_info": {
                    "platform": kol_data.get("platform"),
                    "username": kol_data.get("username"),
                    "name": kol_data.get("name"),
                    "subsCount": kol_data.get("subsCount"),
                    "country": kol_data.get("country")
                },
                "quota": data.get("quota"),
                "usage": data.get("usage"),
                "cost": data.get("cost")
            }

        except aiohttp.ClientError as e:
            logger.error(f"Nano API异步请求失败: {str(e)}")
            return {
                "success": False,
                "message": f"请求失败: {str(e)}",
                "email": None
            }
        except Exception as e:
            logger.error(f"处理Nano API异步响应时发生异常: {str(e)}")
            return {
                "success": False,
                "message": f"处理异常: {str(e)}",
                "email": None
            }

    def update_kol_email(self, db: Session, kol_id: int, email: str) -> Dict[str, Any]:
        """
        更新KOL主表中的邮箱字段

        Args:
            db: 数据库会话
            kol_id: KOL ID
            email: 邮箱地址

        Returns:
            更新结果
        """
        try:
            # 查找KOL记录（仅根据kol_id查找）
            kol = kol_crud.get_by_kol_id(db, kol_id=kol_id)
            if not kol:
                return {
                    "success": False,
                    "message": f"未找到KOL: {kol_id}"
                }
            
            # 更新邮箱字段
            update_data = {"email": email}
            updated_kol = kol_crud.update(db, db_obj=kol, obj_in=update_data)
            
            logger.info(f"成功更新KOL {kol_id} 的邮箱: {email}")
            
            return {
                "success": True,
                "message": "邮箱更新成功",
                "kol_id": kol_id,
                "old_email": kol.email,
                "new_email": updated_kol.email
            }
            
        except Exception as e:
            logger.error(f"更新KOL邮箱失败: {str(e)}")
            return {
                "success": False,
                "message": f"更新失败: {str(e)}"
            }
    
    def get_and_update_kol_email(
        self, 
        db: Session, 
        kol_id: int, 
        url: str,
        force_update: bool = False
    ) -> Dict[str, Any]:
        """
        获取邮箱数据并更新KOL主表（一体化操作）
        
        Args:
            db: 数据库会话
            kol_id: KOL ID
            url: KOL的社交媒体URL
            force_update: 是否强制更新（即使已有邮箱）
            
        Returns:
            操作结果
        """
        try:
            # 检查KOL是否存在（仅根据kol_id查找）
            kol = kol_crud.get_by_kol_id(db, kol_id=kol_id)
            if not kol:
                return {
                    "success": False,
                    "message": f"未找到KOL: {kol_id}",
                    "step": "check_kol"
                }
            
            # 检查是否已有邮箱
            if kol.email and kol.email.strip() and not force_update:
                return {
                    "success": False,
                    "message": f"KOL {kol_id} 已有邮箱: {kol.email}，如需更新请设置force_update=true",
                    "step": "check_existing_email",
                    "existing_email": kol.email
                }
            
            # 调用Nano API获取邮箱
            nano_result = self.get_kol_email_by_url(url)
            
            if not nano_result["success"]:
                return {
                    "success": False,
                    "message": f"获取邮箱失败: {nano_result['message']}",
                    "step": "nano_api_call",
                    "nano_result": nano_result
                }
            
            email = nano_result.get("email")
            if not email:
                return {
                    "success": False,
                    "message": "Nano API未返回有效邮箱",
                    "step": "extract_email",
                    "nano_result": nano_result
                }
            
            # 更新KOL邮箱
            update_result = self.update_kol_email(db, kol_id, email)
            
            if not update_result["success"]:
                return {
                    "success": False,
                    "message": f"更新邮箱失败: {update_result['message']}",
                    "step": "update_email",
                    "email": email
                }
            
            return {
                "success": True,
                "message": "邮箱获取并更新成功",
                "kol_id": kol_id,
                "url": url,
                "old_email": update_result["old_email"],
                "new_email": email,
                "kol_info": nano_result.get("kol_info"),
                "quota_info": {
                    "quota": nano_result.get("quota"),
                    "usage": nano_result.get("usage"),
                    "cost": nano_result.get("cost")
                }
            }
            
        except Exception as e:
            logger.error(f"获取并更新KOL邮箱时发生异常: {str(e)}")
            return {
                "success": False,
                "message": f"操作异常: {str(e)}",
                "step": "exception"
            }


# 便捷函数
def create_nano_service() -> NanoService:
    """创建Nano服务实例"""
    return NanoService()
