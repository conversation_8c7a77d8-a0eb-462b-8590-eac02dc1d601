import os
import tomli
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用配置类"""
    # 项目基本信息
    PROJECT_NAME: str = "KOL 数据管理与分析平台"
    API_V1_STR: str = "/api/v1"
    VERSION: str = "2.1.0"
    
    # 爬取配置
    MODASH_FIRST_PAGE_NUM: int = 0
    MODASH_PAGE_NUM: int = 0

    # 反爬虫配置
    SCRAPING_ENABLE_ANTI_DETECTION: bool = True
    SCRAPING_MIN_DELAY: float = 0.5
    SCRAPING_MAX_DELAY: float = 1.0
    SCRAPING_MAX_CONCURRENT: int = 5
    SCRAPING_MAX_RETRIES: int = 3
    SCRAPING_RETRY_DELAY: float = 2.0
    SCRAPING_TIMEOUT: int = 30
    SCRAPING_USER_AGENT_ROTATION: bool = True

    # User-Agent 列表配置
    SCRAPING_USER_AGENTS: List[str] = [
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]

    # 通用请求头配置
    SCRAPING_COMMON_HEADERS: Dict[str, str] = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        "DNT": "1",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Cache-Control": "max-age=0"
    }
    # CORS配置
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []
    
    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",") if i.strip()]
        elif isinstance(v, list):
            return v
        elif isinstance(v, str):
            return [v]
        return []

    # 数据库配置
    POSTGRES_SERVER: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    POSTGRES_PORT: int
    DATABASE_URI: Optional[str] = None

    @field_validator("DATABASE_URI", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info) -> str:
        if isinstance(v, str) and v:
            return v

        # 从info.data获取其他字段的值
        data = info.data if hasattr(info, 'data') else {}

        # 检查所有必要的PostgreSQL配置项
        required_fields = ["POSTGRES_SERVER", "POSTGRES_USER", "POSTGRES_PASSWORD", "POSTGRES_DB", "POSTGRES_PORT"]
        for field in required_fields:
            if field not in data or not data.get(field):
                raise ValueError(f"缺少必要的数据库配置项: {field}")

        # 手动构建PostgreSQL连接字符串
        username = data.get("POSTGRES_USER")
        password = data.get("POSTGRES_PASSWORD")
        host = data.get("POSTGRES_SERVER")
        port = data.get("POSTGRES_PORT")
        database = data.get("POSTGRES_DB")

        return f"postgresql+psycopg2://{username}:{password}@{host}:{port}/{database}"
    
    # Redis配置
    REDIS_HOST: str
    REDIS_PORT: int
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int
    REDIS_URI: Optional[str] = None

    @field_validator("REDIS_URI", mode="before")
    @classmethod
    def assemble_redis_connection(cls, v: Optional[str], info) -> str:
        if isinstance(v, str) and v:
            return v

        # 从info.data获取其他字段的值
        data = info.data if hasattr(info, 'data') else {}

        # 检查所有必要的Redis配置项
        required_fields = ["REDIS_HOST", "REDIS_PORT", "REDIS_DB"]
        for field in required_fields:
            if field not in data or data.get(field) is None:
                raise ValueError(f"缺少必要的Redis配置项: {field}")

        # 构建Redis连接字符串
        host = data.get("REDIS_HOST")
        port = data.get("REDIS_PORT")
        password = data.get("REDIS_PASSWORD", "")
        db = data.get("REDIS_DB")

        # 组装Redis URL字符串
        if password:
            return f"redis://:{password}@{host}:{port}/{db}"
        else:
            return f"redis://{host}:{port}/{db}"
    
    
    
    # Celery配置
    CELERY_BROKER_URL: Optional[str] = None
    CELERY_RESULT_BACKEND: Optional[str] = None
    CELERY_WORKER_CONCURRENCY: int = 1
    CELERY_MONITORING_ENABLED: bool = True
    CELERY_TASK_TRACK_STARTED: bool = True
    CELERY_RESULT_EXPIRES: int = 86400  # 结果过期时间，单位秒(1天)
    
    
    @field_validator("CELERY_RESULT_BACKEND", mode="before")
    @classmethod
    def assemble_celery_result_backend(cls, v: Optional[str], info) -> str:
        """组装Celery Result Backend URL (Redis)"""
        if isinstance(v, str) and v:
            return v

        # 从info.data获取其他字段的值
        data = info.data if hasattr(info, 'data') else {}

        # 直接使用Redis配置构建字符串URL
        host = data.get("REDIS_HOST")
        port = data.get("REDIS_PORT")
        password = data.get("REDIS_PASSWORD", "")
        db = data.get("REDIS_DB")

        if not all([host, port, db is not None]):
            required_fields = ["REDIS_HOST", "REDIS_PORT", "REDIS_DB"]
            for field in required_fields:
                if field not in data or data.get(field) is None:
                    raise ValueError(f"缺少必要的Redis配置项: {field}")

        # 组装Redis URL字符串
        if password:
            return f"redis://:{password}@{host}:{port}/{db}"
        else:
            return f"redis://{host}:{port}/{db}"
    
    # RedBeat配置
    REDBEAT_KEY_PREFIX: str = "redbeat:"
    REDBEAT_LOCK_KEY: str = "redbeat:lock:"
    REDBEAT_LOCK_TIMEOUT: int = 60
    
    # TikHub API配置
    TIKHUB_TOKEN: str = "xacqaFZO6SIXljVf6mKMul9v/SEVtrzmuotC+SzipUZ5r1dVYtduzyCZUw=="
    TIKHUB_INSTAGRAM_USER_INFO_URL: str = "https://api.tikhub.io/api/v1/instagram/web_app/fetch_user_info_by_user_id"
    TIKHUB_MAX_CONCURRENT: int = 8  # TikHub API最大并发数
    # 异步并发控制配置
    ASYNC_MAX_CONCURRENCY: int = 10  # 异步接口最大并发数

    # 文件上传配置
    UPLOAD_DIR: str = "uploads"  # 文件上传目录
    PAYMENT_SCREENSHOT_DIR: str = "payment_screenshots"  # 支付截图目录
    MAX_FILE_SIZE: int = 5 * 1024 * 1024  # 最大文件大小 5MB
    ALLOWED_FILE_EXTENSIONS: List[str] = [".jpg", ".jpeg", ".png", ".pdf"]  # 允许的文件扩展名

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE_PATH: str = "logs"
    LOG_FILE_NAME: str = "kol_platform.log"
    LOG_MAX_BYTES: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5  # 保留5个备份文件

    
    @property
    def log_file(self) -> str:
        """获取完整的日志文件路径"""
        os.makedirs(self.LOG_FILE_PATH, exist_ok=True)
        return os.path.join(self.LOG_FILE_PATH, self.LOG_FILE_NAME)

    @property
    def payment_screenshot_path(self) -> str:
        """获取支付截图存储路径"""
        full_path = os.path.join(self.UPLOAD_DIR, self.PAYMENT_SCREENSHOT_DIR)
        os.makedirs(full_path, exist_ok=True)
        return full_path
    
    model_config = SettingsConfigDict(
        case_sensitive=True,
        extra="ignore",
        env_file=".env",
        env_file_encoding="utf-8",
    )


def load_toml_config(config_path: str = "config.toml") -> Dict[str, Any]:
    """加载TOML配置文件"""
    try:
        with open(config_path, "rb") as f:
            return tomli.load(f)
    except FileNotFoundError:
        print(f"警告: 配置文件 {config_path} 不存在，将使用默认配置")
        return {}
    except Exception as e:
        print(f"错误: 加载配置文件 {config_path} 失败: {e}")
        return {}


def create_settings_from_toml() -> Settings:
    """从TOML配置文件创建Settings实例"""
    # 先检查环境变量中是否有配置路径
    config_path = os.environ.get("CONFIG_PATH", "config.toml")
    config_data = load_toml_config(config_path)
    
    # 展平嵌套的配置项
    flat_config = {}
    for key, value in config_data.items():
        if isinstance(value, dict):
            for subkey, subvalue in value.items():
                flat_config[subkey] = subvalue
        else:
            flat_config[key] = value
    
    # 检查端口号类型
    port_fields = ["POSTGRES_PORT", "REDIS_PORT"]
    for field in port_fields:
        if field in flat_config and not isinstance(flat_config[field], int):
            try:
                flat_config[field] = int(flat_config[field])
            except (ValueError, TypeError):
                raise ValueError(f"错误: 无法将 {field} 的值 '{flat_config[field]}' 转换为整数")
    
    # 首先创建Settings实例，此时会自动从环境变量中读取值
    settings_instance = Settings(**flat_config)
    return settings_instance


# 创建全局设置实例
settings = create_settings_from_toml()


