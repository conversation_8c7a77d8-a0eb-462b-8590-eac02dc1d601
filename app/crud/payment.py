"""
支付CRUD操作
"""
from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.crud.base import CRUDBase
from app.models.payment import Payment
from app.schemas.payment import PaymentCreate, PaymentUpdate, PaymentCreateInternal


class CRUDPayment(CRUDBase[Payment, PaymentCreate, PaymentUpdate]):
    """支付CRUD操作类"""

    def create_internal(self, db: Session, *, obj_in: PaymentCreateInternal) -> Payment:
        """使用内部schema创建支付记录"""
        return self.create(db, obj_in=obj_in)
    
    def get_by_performance(
        self,
        db: Session,
        *,
        performance_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """根据绩效ID获取支付记录"""
        return (
            db.query(Payment)
            .filter(Payment.performance_id == performance_id)
            .order_by(Payment.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_tracker(
        self,
        db: Session,
        *,
        tracker: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """根据跟进人获取支付记录"""
        return (
            db.query(Payment)
            .filter(Payment.tracker == tracker)
            .order_by(Payment.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_date_range(
        self,
        db: Session,
        *,
        start_date: datetime,
        end_date: datetime,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """根据支付日期范围获取支付记录"""
        return (
            db.query(Payment)
            .filter(
                Payment.payout_date >= start_date,
                Payment.payout_date <= end_date
            )
            .order_by(Payment.payout_date.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_amount_range(
        self,
        db: Session,
        *,
        min_amount: Optional[Decimal] = None,
        max_amount: Optional[Decimal] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """根据支付金额范围获取支付记录"""
        query = db.query(Payment).filter(Payment.payment_amount.isnot(None))
        
        if min_amount is not None:
            query = query.filter(Payment.payment_amount >= min_amount)
        
        if max_amount is not None:
            query = query.filter(Payment.payment_amount <= max_amount)
        
        return (
            query.order_by(Payment.payment_amount.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_paypal_account(
        self,
        db: Session,
        *,
        paypal_account: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """根据PayPal账户获取支付记录"""
        return (
            db.query(Payment)
            .filter(Payment.paypal_accounts.ilike(f"%{paypal_account}%"))
            .order_by(Payment.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_pending_payments(self, db: Session, *, limit: int = 100) -> List[Payment]:
        """获取待支付的记录（没有支付日期的）"""
        return (
            db.query(Payment)
            .filter(Payment.payout_date.is_(None))
            .order_by(Payment.created_at.asc())
            .limit(limit)
            .all()
        )
    
    def get_completed_payments(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """获取已完成的支付记录"""
        return (
            db.query(Payment)
            .filter(Payment.payout_date.isnot(None))
            .order_by(Payment.payout_date.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def search_payments(
        self,
        db: Session,
        *,
        performance_id: Optional[int] = None,
        tracker: Optional[str] = None,
        paypal_account: Optional[str] = None,
        fund_source: Optional[str] = None,
        min_amount: Optional[Decimal] = None,
        max_amount: Optional[Decimal] = None,
        has_payout_date: Optional[bool] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """搜索支付记录"""
        query = db.query(Payment)
        
        if performance_id:
            query = query.filter(Payment.performance_id == performance_id)
        
        if tracker:
            query = query.filter(Payment.tracker.ilike(f"%{tracker}%"))
        
        if paypal_account:
            query = query.filter(Payment.paypal_accounts.ilike(f"%{paypal_account}%"))
        
        if fund_source:
            query = query.filter(Payment.fund_source.ilike(f"%{fund_source}%"))
        
        if min_amount is not None:
            query = query.filter(Payment.payment_amount >= min_amount)
        
        if max_amount is not None:
            query = query.filter(Payment.payment_amount <= max_amount)
        
        if has_payout_date is not None:
            if has_payout_date:
                query = query.filter(Payment.payout_date.isnot(None))
            else:
                query = query.filter(Payment.payout_date.is_(None))
        
        return (
            query.order_by(Payment.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def mark_as_paid(
        self,
        db: Session,
        *,
        payment_id: int,
        payout_date: Optional[datetime] = None
    ) -> Optional[Payment]:
        """标记为已支付"""
        db_obj = self.get(db, id=payment_id)
        if not db_obj:
            return None
        
        if not payout_date:
            payout_date = datetime.utcnow()
        
        return self.update(db, db_obj=db_obj, obj_in={"payout_date": payout_date})
    
    def get_payment_statistics(self, db: Session) -> dict:
        """获取支付统计信息"""
        total = db.query(Payment).count()
        completed = db.query(Payment).filter(Payment.payout_date.isnot(None)).count()
        pending = total - completed
        
        # 金额统计
        amount_stats = db.query(
            func.sum(Payment.payment_amount).label('total_amount'),
            func.avg(Payment.payment_amount).label('avg_amount'),
            func.min(Payment.payment_amount).label('min_amount'),
            func.max(Payment.payment_amount).label('max_amount')
        ).filter(Payment.payment_amount.isnot(None)).first()
        
        # 已支付金额统计
        paid_amount_stats = db.query(
            func.sum(Payment.payment_amount).label('total_paid'),
            func.count(Payment.id).label('paid_count')
        ).filter(
            Payment.payment_amount.isnot(None),
            Payment.payout_date.isnot(None)
        ).first()
        
        # 按跟进人统计
        tracker_stats = {}
        trackers = db.query(Payment.tracker).filter(
            Payment.tracker.isnot(None)
        ).distinct().all()
        
        for (tracker_name,) in trackers:
            count = db.query(Payment).filter(Payment.tracker == tracker_name).count()
            total_amount = db.query(func.sum(Payment.payment_amount)).filter(
                Payment.tracker == tracker_name,
                Payment.payment_amount.isnot(None)
            ).scalar() or 0
            tracker_stats[tracker_name] = {
                "count": count,
                "total_amount": float(total_amount)
            }
        
        return {
            "total": total,
            "completed": completed,
            "pending": pending,
            "completion_rate": round(completed / total * 100, 2) if total > 0 else 0,
            "amount_stats": {
                "total_amount": float(amount_stats.total_amount) if amount_stats.total_amount else 0,
                "avg_amount": float(amount_stats.avg_amount) if amount_stats.avg_amount else 0,
                "min_amount": float(amount_stats.min_amount) if amount_stats.min_amount else 0,
                "max_amount": float(amount_stats.max_amount) if amount_stats.max_amount else 0
            },
            "paid_stats": {
                "total_paid": float(paid_amount_stats.total_paid) if paid_amount_stats.total_paid else 0,
                "paid_count": paid_amount_stats.paid_count or 0
            },
            "tracker_stats": tracker_stats
        }


# 创建CRUD实例
payment = CRUDPayment(Payment)
