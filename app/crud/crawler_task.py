"""
爬虫任务CRUD操作
"""
from asyncio import CancelledError
from typing import List, Optional
from decimal import Decimal
from sqlalchemy.orm import Session


from app.crud.base import CRUDBase
from app.models.crawler_task import CrawlerTask
from app.models.enums import PlatformEnum, CrawlerTaskStatusEnum
from app.schemas.crawler_task import CrawlerTaskCreate, CrawlerTaskUpdate


class CRUDCrawlerTask(CRUDBase[CrawlerTask, CrawlerTaskCreate, CrawlerTaskUpdate]):
    """爬虫任务CRUD操作类"""
    
    def get_by_project(
        self,
        db: Session,
        *,
        project_code: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[CrawlerTask]:
        """根据项目代码获取爬虫任务列表 - 使用idx_crawler_tasks_project_code索引"""
        return (
            db.query(CrawlerTask)
            .filter(CrawlerTask.project_code == project_code)
            .order_by(CrawlerTask.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_status(
        self,
        db: Session,
        *,
        status: CrawlerTaskStatusEnum,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[CrawlerTask]:
        """根据状态获取爬虫任务 - 优化使用idx_crawler_tasks_project_status复合索引"""
        if project_code:
            # 使用复合索引 idx_crawler_tasks_project_status (project_code, status)
            query = db.query(CrawlerTask).filter(
                CrawlerTask.project_code == project_code,
                CrawlerTask.status == status
            )
        else:
            # 使用单列索引 idx_crawler_tasks_status
            query = db.query(CrawlerTask).filter(CrawlerTask.status == status)
        
        return (
            query.order_by(CrawlerTask.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_platform(
        self,
        db: Session,
        *,
        platform: PlatformEnum,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[CrawlerTask]:
        """根据平台获取爬虫任务"""
        query = db.query(CrawlerTask).filter(CrawlerTask.platform == platform)
        
        if project_code:
            query = query.filter(CrawlerTask.project_code == project_code)
        
        return (
            query.order_by(CrawlerTask.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_running_tasks(self, db: Session) -> List[CrawlerTask]:
        """获取正在运行的任务"""
        return (
            db.query(CrawlerTask)
            .filter(CrawlerTask.status == CrawlerTaskStatusEnum.RUNNING)
            .order_by(CrawlerTask.created_at.desc())
            .all()
        )
   
    def get_pending_tasks(self, db: Session, *, limit: int = 10) -> List[CrawlerTask]:
        """获取待执行的任务"""
        return (
            db.query(CrawlerTask)
            .filter(CrawlerTask.status == CrawlerTaskStatusEnum.PENDING)
            .order_by(CrawlerTask.created_at.asc())
            .limit(limit)
            .all()
        )
    
    def update_status(
        self,
        db: Session,
        *,
        task_id: int,
        status: CrawlerTaskStatusEnum,
        log_msg: Optional[str] = None,
        append_log: bool = True
    ) -> Optional[CrawlerTask]:
        """更新任务状态

        Args:
            db: 数据库会话
            task_id: 任务ID
            status: 新的任务状态
            log_msg: 日志消息
            append_log: 是否追加日志消息（True=追加，False=覆盖）
        """
        db_obj = self.get(db, id=task_id)
        if not db_obj:
            return None

        update_data = {"status": status}
        if log_msg is not None:
            # 处理log_msg的追加逻辑
            if append_log and db_obj.log_msg:
                # 如果需要追加且已有log_msg，则追加新消息
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                final_log_msg = f"{db_obj.log_msg}\n[{timestamp}] {log_msg}"
            else:
                # 如果不需要追加或没有log_msg，则直接使用新消息
                final_log_msg = log_msg

            update_data["log_msg"] = final_log_msg

        return self.update(db, db_obj=db_obj, obj_in=update_data)
    
    def search_tasks(
        self,
        db: Session,
        *,
        task_name: Optional[str] = None,
        platform: Optional[PlatformEnum] = None,
        status: Optional[CrawlerTaskStatusEnum] = None,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[CrawlerTask]:
        """搜索爬虫任务"""
        query = db.query(CrawlerTask)
        
        if task_name:
            query = query.filter(CrawlerTask.task_name.ilike(f"%{task_name}%"))
        
        if platform:
            query = query.filter(CrawlerTask.platform == platform)
        
        if status:
            query = query.filter(CrawlerTask.status == status)
        
        if project_code:
            query = query.filter(CrawlerTask.project_code == project_code)
        
        return (
            query.order_by(CrawlerTask.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_task_statistics(self, db: Session, *, project_code: Optional[str] = None) -> dict:
        """获取任务统计信息"""
        query = db.query(CrawlerTask)

        if project_code:
            query = query.filter(CrawlerTask.project_code == project_code)

        total = query.count()
        pending = query.filter(CrawlerTask.status == CrawlerTaskStatusEnum.PENDING).count()
        running = query.filter(CrawlerTask.status == CrawlerTaskStatusEnum.RUNNING).count()
        completed = query.filter(CrawlerTask.status == CrawlerTaskStatusEnum.COMPLETED).count()
        failed = query.filter(CrawlerTask.status == CrawlerTaskStatusEnum.FAILED).count()
        cancelled = query.filter(CrawlerTask.status == CrawlerTaskStatusEnum.CANCELLED).count()

        return {
            "total": total,
            "pending": pending,
            "running": running,
            "completed": completed,
            "failed": failed,
            "cancelled": cancelled
        }

    def update_progress(
        self,
        db: Session,
        *,
        task_id: int,
        progress: int,
        log_msg: Optional[str] = None,
        status: Optional[CrawlerTaskStatusEnum] = None,
        total_duration: Optional[float] = None
    ) -> Optional[CrawlerTask]:
        """更新任务进度"""
        db_task = self.get(db, id=task_id)
        if not db_task:
            return None

        # 更新进度
        db_task.task_progress = progress

        # 更新日志信息
        if log_msg is not None:
            db_task.log_msg = log_msg

        # 更新状态
        if status is not None:
            db_task.status = status

        # 更新总耗时
        if total_duration is not None:
            db_task.total_duration = Decimal(str(total_duration))

        db.commit()
        db.refresh(db_task)
        return db_task


# 创建CRUD实例
crawler_task = CRUDCrawlerTask(CrawlerTask)
