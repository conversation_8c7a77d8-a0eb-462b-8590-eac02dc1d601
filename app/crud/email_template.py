"""
邮件模板CRUD操作
"""
from typing import List, Optional
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.email_template import EmailTemplate
from app.schemas.email_template import EmailTemplateCreate, EmailTemplateUpdate


class CRUDEmailTemplate(CRUDBase[EmailTemplate, EmailTemplateCreate, EmailTemplateUpdate]):
    """邮件模板CRUD操作类"""
    
    def get_by_code(self, db: Session, *, code: str) -> Optional[EmailTemplate]:
        """根据模板代码获取模板"""
        return db.query(EmailTemplate).filter(EmailTemplate.code == code).first()

    def get_by_code_and_project(self, db: Session, *, code: str, project_code: str) -> Optional[EmailTemplate]:
        """根据模板代码和项目代码获取模板"""
        return db.query(EmailTemplate).filter(
            EmailTemplate.code == code,
            EmailTemplate.project_code == project_code
        ).first()
    
    def get_by_project(
        self,
        db: Session,
        *,
        project_code: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[EmailTemplate]:
        """根据项目代码获取模板列表"""
        return (
            db.query(EmailTemplate)
            .filter(EmailTemplate.project_code == project_code)
            .order_by(EmailTemplate.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_name_pattern(
        self,
        db: Session,
        *,
        name_pattern: str,
        project_code: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[EmailTemplate]:
        """根据模板名称模糊查询"""
        query = db.query(EmailTemplate).filter(
            EmailTemplate.name.ilike(f"%{name_pattern}%")
        )

        if project_code:
            query = query.filter(EmailTemplate.project_code == project_code)

        return query.order_by(EmailTemplate.created_at.desc()).offset(skip).limit(limit).all()
    
    def create_template(self, db: Session, *, obj_in: EmailTemplateCreate) -> EmailTemplate:
        """创建邮件模板，检查代码和项目组合唯一性"""
        # 检查模板代码和项目组合是否已存在
        existing = (
            db.query(EmailTemplate)
            .filter(
                EmailTemplate.code == obj_in.code,
                EmailTemplate.project_code == obj_in.project_code
            )
            .first()
        )
        if existing:
            raise ValueError(f"项目 '{obj_in.project_code}' 中已存在模板代码 '{obj_in.code}'")

        return self.create(db, obj_in=obj_in)
    
    def update_template(
        self,
        db: Session,
        *,
        template_id: int,
        obj_in: EmailTemplateUpdate
    ) -> Optional[EmailTemplate]:
        """根据模板ID更新模板"""
        db_obj = self.get(db, id=template_id)
        if not db_obj:
            return None

        return self.update(db, db_obj=db_obj, obj_in=obj_in)

    def delete_template(self, db: Session, *, template_id: int) -> Optional[EmailTemplate]:
        """根据模板ID删除模板"""
        db_obj = self.get(db, id=template_id)
        if not db_obj:
            return None

        db.delete(db_obj)
        db.commit()
        return db_obj
    
    def get_templates_with_usage(self, db: Session, *, project_code: str) -> List[dict]:
        """获取模板列表及其使用统计"""
        from app.models.email_send import EmailSend
        
        templates = self.get_by_project(db, project_code=project_code)
        result = []
        
        for template in templates:
            # 统计使用次数
            usage_count = (
                db.query(EmailSend)
                .filter(EmailSend.template_code == template.code)
                .count()
            )
            
            result.append({
                "template": template,
                "usage_count": usage_count
            })
        
        return result


# 创建CRUD实例
email_template = CRUDEmailTemplate(EmailTemplate)
