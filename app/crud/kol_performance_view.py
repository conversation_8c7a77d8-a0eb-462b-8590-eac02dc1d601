"""
KOL表现视图的CRUD操作
"""
from typing import List, Optional
from decimal import Decimal
from datetime import datetime, date
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.models.enums import PlatformEnum
from app.schemas.kol_performance_view import KOLPerformanceSearch


def get_kol_performance_view(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    platform: Optional[PlatformEnum] = None,
    project_code: Optional[str] = None,
    status: Optional[str] = None,
    min_cpm: Optional[Decimal] = None,
    max_cpm: Optional[Decimal] = None,
    min_views: Optional[int] = None,
    max_views: Optional[int] = None,
) -> List[dict]:
    """
    查询KOL表现视图数据
    """
    # 构建基础查询
    query = "SELECT * FROM kol_performance_view WHERE 1=1"
    params = {}
    
    # 添加过滤条件
    if platform:
        query += " AND platform = :platform"
        params["platform"] = platform.value
    
    if project_code:
        query += " AND project_code = :project_code"
        params["project_code"] = project_code
    
    if status:
        query += " AND status = :status"
        params["status"] = status
    
    if min_cpm is not None:
        query += " AND cpm >= :min_cpm"
        params["min_cpm"] = min_cpm
    
    if max_cpm is not None:
        query += " AND cpm <= :max_cpm"
        params["max_cpm"] = max_cpm
    
    if min_views is not None:
        query += " AND views_total >= :min_views"
        params["min_views"] = min_views
    
    if max_views is not None:
        query += " AND views_total <= :max_views"
        params["max_views"] = max_views
    
    # 添加排序和分页
    query += " ORDER BY created_at DESC LIMIT :limit OFFSET :skip"
    params["limit"] = limit
    params["skip"] = skip
    
    # 执行查询
    result = db.execute(text(query), params)
    
    # 转换结果为字典列表
    columns = result.keys()
    rows = result.fetchall()
    
    return [dict(zip(columns, row)) for row in rows]


def count_kol_performance_view(
    db: Session,
    platform: Optional[PlatformEnum] = None,
    project_code: Optional[str] = None,
    status: Optional[str] = None,
    min_cpm: Optional[Decimal] = None,
    max_cpm: Optional[Decimal] = None,
    min_views: Optional[int] = None,
    max_views: Optional[int] = None,
) -> int:
    """
    统计KOL表现视图数据总数
    """
    # 构建基础查询
    query = "SELECT COUNT(*) as total FROM kol_performance_view WHERE 1=1"
    params = {}
    
    # 添加过滤条件
    if platform:
        query += " AND platform = :platform"
        params["platform"] = platform.value
    
    if project_code:
        query += " AND project_code = :project_code"
        params["project_code"] = project_code
    
    if status:
        query += " AND status = :status"
        params["status"] = status
    
    if min_cpm is not None:
        query += " AND cpm >= :min_cpm"
        params["min_cpm"] = min_cpm
    
    if max_cpm is not None:
        query += " AND cpm <= :max_cpm"
        params["max_cpm"] = max_cpm
    
    if min_views is not None:
        query += " AND views_total >= :min_views"
        params["min_views"] = min_views
    
    if max_views is not None:
        query += " AND views_total <= :max_views"
        params["max_views"] = max_views
    
    # 执行查询
    result = db.execute(text(query), params)
    return result.scalar()


def search_kol_performance_view(
    db: Session,
    search_params: KOLPerformanceSearch,
    skip: int = 0,
    limit: int = 100,
) -> List[dict]:
    """
    高级搜索KOL表现视图数据
    """
    # 构建基础查询
    query = "SELECT * FROM kol_performance_view WHERE 1=1"
    params = {}

    # 平台筛选
    if search_params.platform:
        query += " AND platform = :platform"
        params["platform"] = search_params.platform.value
    elif search_params.platforms:
        platform_values = [p.value for p in search_params.platforms]
        placeholders = ",".join([f":platform_{i}" for i in range(len(platform_values))])
        query += f" AND platform IN ({placeholders})"
        for i, platform in enumerate(platform_values):
            params[f"platform_{i}"] = platform

    # 项目编码筛选
    if search_params.project_code:
        query += " AND project_code = :project_code"
        params["project_code"] = search_params.project_code
    elif search_params.project_codes:
        placeholders = ",".join([f":project_code_{i}" for i in range(len(search_params.project_codes))])
        query += f" AND project_code IN ({placeholders})"
        for i, code in enumerate(search_params.project_codes):
            params[f"project_code_{i}"] = code

    # 状态筛选
    if search_params.status:
        query += " AND status = :status"
        params["status"] = search_params.status
    elif search_params.statuses:
        placeholders = ",".join([f":status_{i}" for i in range(len(search_params.statuses))])
        query += f" AND status IN ({placeholders})"
        for i, status in enumerate(search_params.statuses):
            params[f"status_{i}"] = status

    # KOL信息搜索
    if search_params.social_id:
        query += " AND social_id = :social_id"
        params["social_id"] = search_params.social_id
    elif search_params.social_id_like:
        query += " AND social_id ILIKE :social_id_like"
        params["social_id_like"] = f"%{search_params.social_id_like}%"

    if search_params.nick_name:
        query += " AND nick_name = :nick_name"
        params["nick_name"] = search_params.nick_name
    elif search_params.nick_name_like:
        query += " AND nick_name ILIKE :nick_name_like"
        params["nick_name_like"] = f"%{search_params.nick_name_like}%"

    # 帖子链接搜索
    if search_params.post_link:
        query += " AND post_link = :post_link"
        params["post_link"] = search_params.post_link
    elif search_params.post_link_like:
        query += " AND post_link ILIKE :post_link_like"
        params["post_link_like"] = f"%{search_params.post_link_like}%"

    # 数值范围筛选
    query, params = _add_range_filters(query, params, search_params)

    # 日期范围筛选
    query, params = _add_date_filters(query, params, search_params)

    # 特殊筛选
    query, params = _add_special_filters(query, params, search_params)

    # 排序
    sort_by = search_params.sort_by or "created_at"
    sort_order = search_params.sort_order or "desc"

    # 验证排序字段
    valid_sort_fields = [
        "created_at", "updated_at", "post_date", "views_total",
        "likes_total", "comments_total", "shares_total",
        "engagement_rate", "cpm", "payment_amount"
    ]
    if sort_by not in valid_sort_fields:
        sort_by = "created_at"

    if sort_order.lower() not in ["asc", "desc"]:
        sort_order = "desc"

    query += f" ORDER BY {sort_by} {sort_order.upper()}"

    # 分页
    query += " LIMIT :limit OFFSET :skip"
    params["limit"] = limit
    params["skip"] = skip

    # 执行查询
    result = db.execute(text(query), params)

    # 转换结果为字典列表
    columns = result.keys()
    rows = result.fetchall()

    return [dict(zip(columns, row)) for row in rows]


def _add_range_filters(query: str, params: dict, search_params: KOLPerformanceSearch) -> tuple[str, dict]:
    """添加数值范围筛选条件"""
    # CPM范围
    if search_params.min_cpm is not None:
        query += " AND cpm >= :min_cpm"
        params["min_cpm"] = search_params.min_cpm
    if search_params.max_cpm is not None:
        query += " AND cpm <= :max_cpm"
        params["max_cpm"] = search_params.max_cpm

    # 观看数范围
    if search_params.min_views is not None:
        query += " AND views_total >= :min_views"
        params["min_views"] = search_params.min_views
    if search_params.max_views is not None:
        query += " AND views_total <= :max_views"
        params["max_views"] = search_params.max_views

    # 点赞数范围
    if search_params.min_likes is not None:
        query += " AND likes_total >= :min_likes"
        params["min_likes"] = search_params.min_likes
    if search_params.max_likes is not None:
        query += " AND likes_total <= :max_likes"
        params["max_likes"] = search_params.max_likes

    # 评论数范围
    if search_params.min_comments is not None:
        query += " AND comments_total >= :min_comments"
        params["min_comments"] = search_params.min_comments
    if search_params.max_comments is not None:
        query += " AND comments_total <= :max_comments"
        params["max_comments"] = search_params.max_comments

    # 分享数范围
    if search_params.min_shares is not None:
        query += " AND shares_total >= :min_shares"
        params["min_shares"] = search_params.min_shares
    if search_params.max_shares is not None:
        query += " AND shares_total <= :max_shares"
        params["max_shares"] = search_params.max_shares

    # 互动率范围
    if search_params.min_engagement_rate is not None:
        query += " AND engagement_rate >= :min_engagement_rate"
        params["min_engagement_rate"] = search_params.min_engagement_rate
    if search_params.max_engagement_rate is not None:
        query += " AND engagement_rate <= :max_engagement_rate"
        params["max_engagement_rate"] = search_params.max_engagement_rate

    # 支付金额范围
    if search_params.min_payment_amount is not None:
        query += " AND payment_amount >= :min_payment_amount"
        params["min_payment_amount"] = search_params.min_payment_amount
    if search_params.max_payment_amount is not None:
        query += " AND payment_amount <= :max_payment_amount"
        params["max_payment_amount"] = search_params.max_payment_amount

    return query, params


def _add_date_filters(query: str, params: dict, search_params: KOLPerformanceSearch) -> tuple[str, dict]:
    """添加日期范围筛选条件"""
    # 发布日期范围
    if search_params.post_date_start:
        query += " AND post_date >= :post_date_start"
        params["post_date_start"] = search_params.post_date_start
    if search_params.post_date_end:
        query += " AND post_date <= :post_date_end"
        params["post_date_end"] = search_params.post_date_end

    # 创建时间范围
    if search_params.created_at_start:
        query += " AND created_at >= :created_at_start"
        params["created_at_start"] = search_params.created_at_start
    if search_params.created_at_end:
        query += " AND created_at <= :created_at_end"
        params["created_at_end"] = search_params.created_at_end

    return query, params


def _add_special_filters(query: str, params: dict, search_params: KOLPerformanceSearch) -> tuple[str, dict]:
    """添加特殊筛选条件"""
    # 是否有支付记录
    if search_params.has_payment is not None:
        if search_params.has_payment:
            query += " AND payment_id IS NOT NULL"
        else:
            query += " AND payment_id IS NULL"

    # 是否有互动数据
    if search_params.has_engagement_data is not None:
        if search_params.has_engagement_data:
            query += " AND (likes_total IS NOT NULL OR comments_total IS NOT NULL OR shares_total IS NOT NULL)"
        else:
            query += " AND likes_total IS NULL AND comments_total IS NULL AND shares_total IS NULL"

    return query, params


def count_search_kol_performance_view(
    db: Session,
    search_params: KOLPerformanceSearch,
) -> int:
    """
    统计高级搜索KOL表现视图数据总数
    """
    # 构建基础查询
    query = "SELECT COUNT(*) as total FROM kol_performance_view WHERE 1=1"
    params = {}

    # 平台筛选
    if search_params.platform:
        query += " AND platform = :platform"
        params["platform"] = search_params.platform.value
    elif search_params.platforms:
        platform_values = [p.value for p in search_params.platforms]
        placeholders = ",".join([f":platform_{i}" for i in range(len(platform_values))])
        query += f" AND platform IN ({placeholders})"
        for i, platform in enumerate(platform_values):
            params[f"platform_{i}"] = platform

    # 项目编码筛选
    if search_params.project_code:
        query += " AND project_code = :project_code"
        params["project_code"] = search_params.project_code
    elif search_params.project_codes:
        placeholders = ",".join([f":project_code_{i}" for i in range(len(search_params.project_codes))])
        query += f" AND project_code IN ({placeholders})"
        for i, code in enumerate(search_params.project_codes):
            params[f"project_code_{i}"] = code

    # 状态筛选
    if search_params.status:
        query += " AND status = :status"
        params["status"] = search_params.status
    elif search_params.statuses:
        placeholders = ",".join([f":status_{i}" for i in range(len(search_params.statuses))])
        query += f" AND status IN ({placeholders})"
        for i, status in enumerate(search_params.statuses):
            params[f"status_{i}"] = status

    # KOL信息搜索
    if search_params.social_id:
        query += " AND social_id = :social_id"
        params["social_id"] = search_params.social_id
    elif search_params.social_id_like:
        query += " AND social_id ILIKE :social_id_like"
        params["social_id_like"] = f"%{search_params.social_id_like}%"

    if search_params.nick_name:
        query += " AND nick_name = :nick_name"
        params["nick_name"] = search_params.nick_name
    elif search_params.nick_name_like:
        query += " AND nick_name ILIKE :nick_name_like"
        params["nick_name_like"] = f"%{search_params.nick_name_like}%"

    # 帖子链接搜索
    if search_params.post_link:
        query += " AND post_link = :post_link"
        params["post_link"] = search_params.post_link
    elif search_params.post_link_like:
        query += " AND post_link ILIKE :post_link_like"
        params["post_link_like"] = f"%{search_params.post_link_like}%"

    # 数值范围筛选
    query, params = _add_range_filters(query, params, search_params)

    # 日期范围筛选
    query, params = _add_date_filters(query, params, search_params)

    # 特殊筛选
    query, params = _add_special_filters(query, params, search_params)

    # 执行查询
    result = db.execute(text(query), params)
    return result.scalar()
