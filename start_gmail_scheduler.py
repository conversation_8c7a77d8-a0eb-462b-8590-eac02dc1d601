#!/usr/bin/env python3
"""
启动Gmail同步定时任务调度器
每天晚上8点自动同步Gmail数据到candidates表
"""
import sys
import os
import signal
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.schedule.gmail_sync_scheduler import start_gmail_sync_scheduler, stop_gmail_sync_scheduler

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n收到停止信号，正在关闭调度器...")
    sys.exit(0)

def main(project_code: str = "OOG120", cron_expr: str = "0 20 * * *"):
    """主函数

    Args:
        project_code: 项目编码
        cron_expr: Cron表达式
    """
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    print("=== Gmail同步定时任务调度器 ===")
    print(f"任务配置: {cron_expr} (Asia/Shanghai) 同步Gmail数据")
    print(f"项目: {project_code}")
    print("按 Ctrl+C 停止调度器")
    print("=" * 40)

    scheduler = None

    try:
        # 启动调度器
        scheduler, api = start_gmail_sync_scheduler(project_code, cron_expr)

        # 显示任务信息
        task_id = f"gmail_sync_{project_code.lower()}"
        task_info = api.get_task_info(task_id)
        if task_info.get('success'):
            data = task_info['data']
            print(f"任务ID: {data['task_id']}")
            print(f"Cron表达式: {data['config']['cron_expr']}")
            print(f"下次运行时间: {data['next_run_time']}")
            print(f"任务状态: {'运行中' if data['is_running'] else '等待中'}")

        print("\n调度器已启动，等待执行...")

        # 保持运行
        import time
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n收到中断信号...")
    except Exception as e:
        print(f"调度器运行出错: {e}")
    finally:
        if scheduler:
            stop_gmail_sync_scheduler(scheduler)
        print("调度器已停止")

if __name__ == "__main__":
    import sys

    # 从命令行参数获取项目编码和cron表达式
    project_code = sys.argv[1] if len(sys.argv) > 1 else "OOG120"
    cron_expr = sys.argv[2] if len(sys.argv) > 2 else "0 20 * * *"

    main(project_code, cron_expr)
